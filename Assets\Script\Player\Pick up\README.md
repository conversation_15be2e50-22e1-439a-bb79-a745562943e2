# 🎯 HỆ THỐNG PICKUP

## 📋 Tổng Quan

Hệ thống pickup hoàn chỉnh cho Unity với đầy đủ tính năng:
- ✅ Phát hiện vật thể bằng raycast từ camera
- ✅ Tương tác bằng phím E/F
- ✅ UI hiển thị thông tin vật phẩm
- ✅ Visual feedback (highlight, particle effects)
- ✅ Audio effects và screen shake
- ✅ Cấu hình đầy đủ trong Inspector (Edit mode)
- ✅ Tuân thủ Unity coding conventions

---

## 🚀 Cài Đặt Nhanh

### Bước 1: Thêm vào Player
1. Chọn **Player GameObject** trong Hierarchy
2. Add Component → **PickupSystemManager**
3. Trong Inspector, thiết lập các references cần thiết
4. Nhấn **"Thiết Lập Hệ Thống"** trong Context Menu

### Bước 2: Tạo Pickup UI
1. Tạo Canvas trong scene (nếu chưa có)
2. Tạo Panel con với tên "PickupUI"
3. Thêm TextMeshPro components cho tên và hướng dẫn
4. Add Component → **PickupUI** vào Panel
5. K<PERSON>o thả các UI elements vào script

### Bước 3: Tạo Vật Phẩm Test
1. Tạo GameObject (ví dụ: Cube)
2. Add Component → **PickupItem**
3. Thiết lập tag "Pickupable"
4. Cấu hình thông tin vật phẩm trong Inspector

---

## 📁 Cấu Trúc Files

```
Assets/Script/Player/Pick up/
├── PickupSystem.cs          # Logic chính với raycast detection
├── PickupItem.cs           # Component cho vật phẩm
├── PickupUI.cs             # UI hiển thị prompts
├── PickupEffects.cs        # Visual và audio effects
├── PickupSystemManager.cs  # Manager điều phối
└── README.md              # File này
```

---

## 🎮 Cách Sử Dụng

### Trong Game
- **Nhặt vật phẩm**: Nhìn vào vật phẩm → Nhấn E hoặc F
- **Thả vật phẩm**: Khi đang mang vật → Nhấn E hoặc F
- **Xem thông tin**: Hover chuột lên vật phẩm

### Trong Code

#### Sử Dụng Manager (Khuyến Nghị)
```csharp
// Lấy reference
PickupSystemManager manager = FindObjectOfType<PickupSystemManager>();

// Kiểm tra trạng thái
bool isCarrying = manager.IsCarryingItems();
int itemCount = manager.GetCarriedItemsCount();

// Tạo vật phẩm pickup
GameObject newItem = manager.TaoVatPhamPickup(
    transform.position + Vector3.forward * 2f,
    "wood", 5, "Gỗ", "Nguyên liệu chế tạo"
);

// Thiết lập GameObject thành pickup
PickupItem pickupItem = manager.ThietLapPickupChoGameObject(
    someGameObject, "stone", 3, "Đá", "Vật liệu xây dựng"
);
```

#### Sử Dụng Trực Tiếp
```csharp
// Lấy reference
PickupSystem pickupSystem = FindObjectOfType<PickupSystem>();

// Kiểm tra trạng thái
bool isCarrying = pickupSystem.IsCarryingItems;
int itemCount = pickupSystem.CarriedItemsCount;

// Thiết lập vật phẩm
PickupItem item = someObject.GetComponent<PickupItem>();
item.ThietLapVatPham("wood", 5, "Gỗ", "Nguyên liệu chế tạo");

// Pickup thủ công
bool success = pickupSystem.TryPickupItem(item);
```

---

## ⚙️ Cấu Hình

### PickupSystem Settings
- **Pickup Range**: Khoảng cách tối đa để pickup (3m)
- **Pickup Key**: Phím chính để pickup (E)
- **Alternate Key**: Phím thay thế (F)
- **Max Carry Items**: Số lượng vật phẩm tối đa (1)
- **Detection Frequency**: Tần suất kiểm tra (10Hz)

### PickupItem Settings
- **Item Info**: ID, số lượng, tên hiển thị, mô tả
- **Pickup Settings**: Có thể pickup, tự động thêm vào inventory
- **Visual Effects**: Highlight material, outline color
- **Animation**: Bobbing và rotation effects
- **Audio**: Sound clips cho các sự kiện

### PickupUI Settings
- **UI Components**: Text fields, icons, progress bars
- **Crosshair**: Các trạng thái crosshair khác nhau
- **Animation**: Fade và scale effects
- **Styling**: Màu sắc và layout

### PickupEffects Settings
- **Particle Effects**: Effects cho pickup, drop, highlight
- **Audio Effects**: Sound clips và volume
- **Screen Shake**: Cường độ và thời gian shake
- **Visual Settings**: Màu sắc và số lượng particles

---

## 🔧 Tích Hợp

### Với Economy System
```csharp
// Sẽ tự động tìm PlayerInventory
// Tích hợp với currency và trading systems
```

### Với Crosshair System
```csharp
// Tự động cập nhật crosshair state
// Hiển thị trạng thái tương tác
```

### Với Input System
```csharp
// Hỗ trợ cả keyboard và gamepad
// Configurable key bindings
```

---

## 🎯 Tính Năng Nâng Cao

### Object Pooling
- Tối ưu performance cho particle effects
- Giảm garbage collection

### Raycast Optimization
- Throttled detection frequency
- LayerMask filtering
- Distance culling

### Visual Feedback
- Smooth highlight transitions
- Particle effects
- Screen shake effects
- UI animations

### Audio System
- 3D spatial audio cho vật phẩm
- 2D UI sounds
- Volume controls

---

## 🔍 Troubleshooting

### Vấn Đề Thường Gặp

**Không pickup được:**
- Kiểm tra tag "Pickupable"
- Đảm bảo có Collider
- Kiểm tra LayerMask settings

**UI không hiển thị:**
- Kiểm tra Canvas setup
- Đảm bảo PickupUI references đúng
- Kiểm tra CanvasGroup alpha

**Không có hiệu ứng:**
- Kiểm tra PickupEffects component
- Đảm bảo có AudioSource
- Kiểm tra particle system settings

### Debug Tools
- Context Menu: "Log System Status"
- Context Menu: "Create Test Pickup Item"
- Gizmos hiển thị pickup range
- Debug logs trong Inspector

---

## 📝 Ghi Chú

- Tất cả Inspector fields có thể điều chỉnh trong Edit mode
- Tuân thủ Unity coding conventions
- Hỗ trợ đầy đủ Vietnamese documentation
- Tối ưu performance với throttling và pooling
- Dễ dàng mở rộng và tùy chỉnh

Để được hỗ trợ thêm, vui lòng tham khảo documentation hoặc liên hệ team phát triển.
