using UnityEngine;
using UnityEngine.InputSystem;

namespace PlayerSystem
{
    /// <summary>
    /// Component để tự động sửa các vấn đề thường gặp với Unity Input System
    /// Đảm bảo PlayerInput hoạt động đúng với PlayerInputHandler
    /// </summary>
    public class SimpleInputFixer : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🔧 Auto Fix Settings")]
        [SerializeField, Tooltip("Tự động fix khi Start")]
        private bool m_TuDongFixKhiStart = true;
        
        [SerializeField, Tooltip("Tự động tìm InputSystem_Actions asset")]
        private bool m_TuDongTimInputActions = true;
        
        [SerializeField, Tooltip("Hiển thị debug logs")]
        private bool m_HienThiDebugLogs = true;
        
        [SerializeField, Tooltip("Kiểm tra setup mỗi frame (chỉ trong editor)")]
        private bool m_KiemTraLienTuc = false;

        [Header("📋 Manual References")]
        [SerializeField, Toolt<PERSON>("Input Actions asset (tự động tìm nếu null)")]
        private InputActionAsset m_InputActions;
        
        [SerializeField, Tooltip("Default Action Map name")]
        private string m_DefaultActionMap = "Player";

        [Header("⚙️ Fix Options")]
        [SerializeField, Tooltip("Fix PlayerInput notification behavior")]
        private bool m_FixNotificationBehavior = true;
        
        [SerializeField, Tooltip("Fix missing action map")]
        private bool m_FixActionMap = true;
        
        [SerializeField, Tooltip("Fix missing input actions asset")]
        private bool m_FixInputActionsAsset = true;
        
        [SerializeField, Tooltip("Validate input callbacks")]
        private bool m_ValidateInputCallbacks = true;
        #endregion

        #region Private Fields
        private PlayerInput m_PlayerInput;
        private PlayerInputHandler m_PlayerInputHandler;
        private bool m_DaKhoiTao = false;
        private float m_LastCheckTime = 0f;
        private const float c_CheckInterval = 1f; // Kiểm tra mỗi giây
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            TimCacComponent();
        }

        private void Start()
        {
            if (m_TuDongFixKhiStart)
            {
                FixBasicInputSetup();
            }
        }

        private void Update()
        {
            #if UNITY_EDITOR
            if (m_KiemTraLienTuc && Time.time - m_LastCheckTime > c_CheckInterval)
            {
                m_LastCheckTime = Time.time;
                KiemTraSetup();
            }
            #endif
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Fix cơ bản cho Input System setup
        /// </summary>
        [ContextMenu("Fix Basic Input Setup")]
        public void FixBasicInputSetup()
        {
            if (m_HienThiDebugLogs)
            {
                Debug.Log("[SimpleInputFixer] Bắt đầu fix Input System setup...");
            }

            bool hasChanges = false;

            // Fix PlayerInput component
            if (m_FixNotificationBehavior && FixPlayerInputBehavior())
            {
                hasChanges = true;
            }

            // Fix Input Actions Asset
            if (m_FixInputActionsAsset && FixInputActionsAsset())
            {
                hasChanges = true;
            }

            // Fix Action Map
            if (m_FixActionMap && FixActionMap())
            {
                hasChanges = true;
            }

            // Validate callbacks
            if (m_ValidateInputCallbacks)
            {
                ValidateInputCallbacks();
            }

            m_DaKhoiTao = true;

            if (m_HienThiDebugLogs)
            {
                string result = hasChanges ? "✅ Đã fix thành công" : "ℹ️ Không cần fix";
                Debug.Log($"[SimpleInputFixer] {result}");
            }
        }

        /// <summary>
        /// Kiểm tra setup hiện tại
        /// </summary>
        [ContextMenu("Check Input Setup")]
        public void CheckInputSetup()
        {
            KiemTraSetup();
        }

        /// <summary>
        /// Log thông tin hệ thống
        /// </summary>
        [ContextMenu("Log System Info")]
        public void LogSystemInfo()
        {
            LogThongTinHeThong();
        }

        /// <summary>
        /// Reset và fix lại toàn bộ
        /// </summary>
        [ContextMenu("Reset And Fix All")]
        public void ResetAndFixAll()
        {
            m_DaKhoiTao = false;
            TimCacComponent();
            FixBasicInputSetup();
        }
        #endregion

        #region Private Methods
        private void TimCacComponent()
        {
            m_PlayerInput = GetComponent<PlayerInput>();
            m_PlayerInputHandler = GetComponent<PlayerInputHandler>();

            if (m_PlayerInput == null && m_HienThiDebugLogs)
            {
                Debug.LogWarning("[SimpleInputFixer] Không tìm thấy PlayerInput component!");
            }

            if (m_PlayerInputHandler == null && m_HienThiDebugLogs)
            {
                Debug.LogWarning("[SimpleInputFixer] Không tìm thấy PlayerInputHandler component!");
            }
        }

        private bool FixPlayerInputBehavior()
        {
            if (m_PlayerInput == null) return false;

            bool hasChanges = false;

            // Fix notification behavior
            if (m_PlayerInput.notificationBehavior != PlayerNotifications.SendMessages)
            {
                m_PlayerInput.notificationBehavior = PlayerNotifications.SendMessages;
                hasChanges = true;
                
                if (m_HienThiDebugLogs)
                {
                    Debug.Log("[SimpleInputFixer] Đã fix notification behavior thành SendMessages");
                }
            }

            return hasChanges;
        }

        private bool FixInputActionsAsset()
        {
            if (m_PlayerInput == null) return false;

            bool hasChanges = false;

            // Tìm Input Actions Asset nếu chưa có
            if (m_PlayerInput.actions == null)
            {
                if (m_InputActions == null && m_TuDongTimInputActions)
                {
                    m_InputActions = TimInputActionsAsset();
                }

                if (m_InputActions != null)
                {
                    m_PlayerInput.actions = m_InputActions;
                    hasChanges = true;
                    
                    if (m_HienThiDebugLogs)
                    {
                        Debug.Log($"[SimpleInputFixer] Đã gán Input Actions Asset: {m_InputActions.name}");
                    }
                }
            }

            return hasChanges;
        }

        private bool FixActionMap()
        {
            if (m_PlayerInput == null || m_PlayerInput.actions == null) return false;

            bool hasChanges = false;

            // Fix default action map
            if (string.IsNullOrEmpty(m_PlayerInput.defaultActionMap) || 
                m_PlayerInput.actions.FindActionMap(m_PlayerInput.defaultActionMap) == null)
            {
                var actionMap = m_PlayerInput.actions.FindActionMap(m_DefaultActionMap);
                if (actionMap != null)
                {
                    m_PlayerInput.defaultActionMap = m_DefaultActionMap;
                    hasChanges = true;
                    
                    if (m_HienThiDebugLogs)
                    {
                        Debug.Log($"[SimpleInputFixer] Đã fix default action map: {m_DefaultActionMap}");
                    }
                }
            }

            return hasChanges;
        }

        private InputActionAsset TimInputActionsAsset()
        {
            #if UNITY_EDITOR
            // Tìm trong project
            string[] guids = UnityEditor.AssetDatabase.FindAssets("t:InputActionAsset");
            
            foreach (string guid in guids)
            {
                string path = UnityEditor.AssetDatabase.GUIDToAssetPath(guid);
                InputActionAsset asset = UnityEditor.AssetDatabase.LoadAssetAtPath<InputActionAsset>(path);
                
                if (asset != null)
                {
                    // Ưu tiên asset có tên chứa "InputSystem" hoặc "Player"
                    if (asset.name.Contains("InputSystem") || asset.name.Contains("Player"))
                    {
                        return asset;
                    }
                }
            }
            
            // Nếu không tìm thấy, trả về asset đầu tiên
            if (guids.Length > 0)
            {
                string path = UnityEditor.AssetDatabase.GUIDToAssetPath(guids[0]);
                return UnityEditor.AssetDatabase.LoadAssetAtPath<InputActionAsset>(path);
            }
            #endif

            return null;
        }

        private void ValidateInputCallbacks()
        {
            if (m_PlayerInputHandler == null) return;

            // Kiểm tra các method callbacks có tồn tại không
            var type = m_PlayerInputHandler.GetType();
            
            string[] requiredMethods = { "OnMove", "OnLook", "OnJump", "OnSprint", "OnCrouch" };
            
            foreach (string methodName in requiredMethods)
            {
                var method = type.GetMethod(methodName, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                if (method == null && m_HienThiDebugLogs)
                {
                    Debug.LogWarning($"[SimpleInputFixer] Method {methodName} không tìm thấy trong PlayerInputHandler");
                }
            }
        }

        private void KiemTraSetup()
        {
            if (m_PlayerInput == null || m_PlayerInputHandler == null) return;

            bool hasIssues = false;

            // Kiểm tra notification behavior
            if (m_PlayerInput.notificationBehavior != PlayerNotifications.SendMessages)
            {
                Debug.LogWarning("[SimpleInputFixer] PlayerInput notification behavior không phải SendMessages");
                hasIssues = true;
            }

            // Kiểm tra input actions
            if (m_PlayerInput.actions == null)
            {
                Debug.LogWarning("[SimpleInputFixer] PlayerInput thiếu Input Actions Asset");
                hasIssues = true;
            }

            // Kiểm tra action map
            if (string.IsNullOrEmpty(m_PlayerInput.defaultActionMap))
            {
                Debug.LogWarning("[SimpleInputFixer] PlayerInput thiếu default action map");
                hasIssues = true;
            }

            if (!hasIssues && m_HienThiDebugLogs)
            {
                Debug.Log("[SimpleInputFixer] ✅ Input setup OK");
            }
        }

        private void LogThongTinHeThong()
        {
            System.Text.StringBuilder info = new System.Text.StringBuilder();
            info.AppendLine("=== SIMPLE INPUT FIXER INFO ===");
            
            info.AppendLine($"PlayerInput: {(m_PlayerInput != null ? "✅" : "❌")}");
            info.AppendLine($"PlayerInputHandler: {(m_PlayerInputHandler != null ? "✅" : "❌")}");
            
            if (m_PlayerInput != null)
            {
                info.AppendLine($"Notification Behavior: {m_PlayerInput.notificationBehavior}");
                info.AppendLine($"Input Actions: {(m_PlayerInput.actions != null ? m_PlayerInput.actions.name : "None")}");
                info.AppendLine($"Default Action Map: {m_PlayerInput.defaultActionMap}");
                info.AppendLine($"Current Action Map: {(m_PlayerInput.currentActionMap?.name ?? "None")}");
            }
            
            info.AppendLine($"Đã khởi tạo: {m_DaKhoiTao}");
            
            Debug.Log(info.ToString());
        }
        #endregion
    }
}
