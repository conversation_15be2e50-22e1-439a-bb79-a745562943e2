using UnityEngine;
using UnityEngine.AI;
using System.Collections;

namespace NPCSystem
{
    /// <summary>
    /// AI chính cho NPC mua hàng
    /// Điều khiển hành vi di chuyển, tìm kiếm và mua hàng của NPC
    /// </summary>
    [RequireComponent(typeof(NavMeshAgent))]
    [RequireComponent(typeof(NPCCurrencyManager))]
    public class NPCShoppingAI : MonoBehaviour
    {
        #region Enums
        public enum NPCState
        {
            Idle,           // Đứng yên
            Wandering,      // Đi lang thang
            MovingToShop,   // Di chuyển đến shop
            LookingAtItem,  // Đang xem hàng
            WantToBuy,      // Muốn mua hàng
            WaitingForPlayer, // Chờ player tương tác
            InTransaction   // Đang giao dịch
        }
        #endregion

        #region Serialized Fields
        [Header("🤖 NPC AI Settings")]
        [SerializeField, <PERSON>lt<PERSON>("Tên của NPC")]
        private string m_TenNPC = "Shopping NPC";
        
        [SerializeField, Tooltip("Tốc độ di chuyển")]
        private float m_TocDoChuyenDong = 3.5f;
        
        [SerializeField, Tooltip("Khoảng cách dừng lại")]
        private float m_KhoangCachDung = 1f;
        
        [Header("🛍️ Shopping Behavior")]
        [SerializeField, Tooltip("Thời gian chờ giữa các lần tìm shop")]
        private float m_ThoiGianChoTimShop = 10f;
        
        [SerializeField, Tooltip("Bán kính tìm kiếm shop")]
        private float m_BanKinhTimKiem = 20f;
        
        [SerializeField, Tooltip("Thời gian chờ player tương tác")]
        private float m_ThoiGianChoPlayer = 15f;
        
        [Header("🎯 References")]
        [SerializeField, Tooltip("Component hiển thị text trên đầu")]
        private WorldSpaceTextDisplay m_TextDisplay;
        
        [Header("🔧 Debug")]
        [SerializeField, Tooltip("Hiển thị log debug")]
        private bool m_HienThiLog = true;
        
        [SerializeField, Tooltip("Hiển thị trạng thái hiện tại")]
        private NPCState m_TrangThaiHienTai = NPCState.Idle;
        #endregion

        #region Private Fields
        private NavMeshAgent m_Agent;
        private NPCCurrencyManager m_CurrencyManager;
        private ShopDisplayZone m_CurrentShopZone;
        private Coroutine m_CurrentBehaviorCoroutine;
        private float m_LanTimShopCuoi = 0f;
        private bool m_DaKhoiTao = false;
        private bool m_PlayerCoTheClick = false;
        #endregion

        #region Properties
        public NPCState TrangThaiHienTai => m_TrangThaiHienTai;
        public string TenNPC => m_TenNPC;
        public bool PlayerCoTheClick => m_PlayerCoTheClick;
        public ShopDisplayZone CurrentShopZone => m_CurrentShopZone;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            KhoiTaoComponents();
        }

        private void Start()
        {
            ThietLapAI();
            m_DaKhoiTao = true;
            BatDauHanhVi();
            Log($"NPC {m_TenNPC} đã khởi tạo");
        }

        private void Update()
        {
            if (!m_DaKhoiTao) return;
            
            CapNhatAI();
        }

        private void OnMouseDown()
        {
            if (m_PlayerCoTheClick)
            {
                OnPlayerClick();
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Được gọi khi NPC vào shop zone
        /// </summary>
        public void OnEnterShopZone(ShopDisplayZone shopZone)
        {
            if (shopZone == null) return;
            
            Log($"Vào shop zone: {shopZone.TenHienThi}");
            
            // Kiểm tra có đủ tiền mua không
            if (shopZone.CoTheBanChoNPC(this))
            {
                m_CurrentShopZone = shopZone;
                ChuyenTrangThai(NPCState.MovingToShop);
            }
            else
            {
                Log($"Không đủ tiền mua {shopZone.TenHienThi}");
            }
        }

        /// <summary>
        /// Được gọi khi NPC rời shop zone
        /// </summary>
        public void OnExitShopZone(ShopDisplayZone shopZone)
        {
            if (shopZone == m_CurrentShopZone)
            {
                Log($"Rời shop zone: {shopZone.TenHienThi}");
                m_CurrentShopZone = null;
                
                if (m_TrangThaiHienTai == NPCState.MovingToShop || 
                    m_TrangThaiHienTai == NPCState.LookingAtItem)
                {
                    ChuyenTrangThai(NPCState.Wandering);
                }
            }
        }

        /// <summary>
        /// Được gọi khi player click vào NPC
        /// </summary>
        public void OnPlayerClick()
        {
            if (m_TrangThaiHienTai == NPCState.WaitingForPlayer)
            {
                Log("Player đã click vào NPC");
                ChuyenTrangThai(NPCState.InTransaction);
                
                // Ẩn text "I want to buy..."
                if (m_TextDisplay != null)
                {
                    m_TextDisplay.AnText();
                }
                
                // Trigger camera focus và dialogue
                TriggerCameraFocusAndDialogue();
            }
        }

        /// <summary>
        /// Thực hiện giao dịch mua hàng
        /// </summary>
        public bool ThucHienGiaoDich()
        {
            if (m_CurrentShopZone == null) return false;
            
            bool thanhCong = m_CurrentShopZone.BanHangChoNPC(this);
            
            if (thanhCong)
            {
                Log($"Đã mua {m_CurrentShopZone.TenHienThi} thành công");
                
                // Hiển thị text cảm ơn
                if (m_TextDisplay != null)
                {
                    m_TextDisplay.HienThiText("Thank you!", Color.green, 3f);
                }
                
                // Quay về trạng thái wandering
                StartCoroutine(QuayVeTrangThaiWandering(2f));
            }
            else
            {
                Log("Giao dịch thất bại");
                ChuyenTrangThai(NPCState.Wandering);
            }
            
            return thanhCong;
        }
        #endregion

        #region Private Methods
        private void KhoiTaoComponents()
        {
            m_Agent = GetComponent<NavMeshAgent>();
            m_CurrencyManager = GetComponent<NPCCurrencyManager>();
            
            if (m_TextDisplay == null)
            {
                m_TextDisplay = GetComponentInChildren<WorldSpaceTextDisplay>();
            }
        }

        private void ThietLapAI()
        {
            if (m_Agent != null)
            {
                m_Agent.speed = m_TocDoChuyenDong;
                m_Agent.stoppingDistance = m_KhoangCachDung;
                m_Agent.autoBraking = true;
            }
        }

        private void BatDauHanhVi()
        {
            ChuyenTrangThai(NPCState.Wandering);
        }

        private void CapNhatAI()
        {
            switch (m_TrangThaiHienTai)
            {
                case NPCState.MovingToShop:
                    CapNhatMovingToShop();
                    break;
                    
                case NPCState.WaitingForPlayer:
                    CapNhatWaitingForPlayer();
                    break;
            }
        }

        private void ChuyenTrangThai(NPCState trangThaiMoi)
        {
            if (m_TrangThaiHienTai == trangThaiMoi) return;
            
            Log($"Chuyển trạng thái: {m_TrangThaiHienTai} -> {trangThaiMoi}");
            
            // Dừng behavior hiện tại
            if (m_CurrentBehaviorCoroutine != null)
            {
                StopCoroutine(m_CurrentBehaviorCoroutine);
                m_CurrentBehaviorCoroutine = null;
            }
            
            m_TrangThaiHienTai = trangThaiMoi;
            
            // Bắt đầu behavior mới
            switch (trangThaiMoi)
            {
                case NPCState.Idle:
                    m_CurrentBehaviorCoroutine = StartCoroutine(IdleBehavior());
                    break;
                    
                case NPCState.Wandering:
                    m_CurrentBehaviorCoroutine = StartCoroutine(WanderingBehavior());
                    break;
                    
                case NPCState.MovingToShop:
                    m_CurrentBehaviorCoroutine = StartCoroutine(MovingToShopBehavior());
                    break;
                    
                case NPCState.LookingAtItem:
                    m_CurrentBehaviorCoroutine = StartCoroutine(LookingAtItemBehavior());
                    break;
                    
                case NPCState.WantToBuy:
                    m_CurrentBehaviorCoroutine = StartCoroutine(WantToBuyBehavior());
                    break;
            }
        }

        private void CapNhatMovingToShop()
        {
            if (m_CurrentShopZone == null)
            {
                ChuyenTrangThai(NPCState.Wandering);
                return;
            }
            
            // Kiểm tra đã đến vị trí chưa
            if (m_CurrentShopZone.NPCOViTriMuaHang(this))
            {
                ChuyenTrangThai(NPCState.LookingAtItem);
            }
        }

        private void CapNhatWaitingForPlayer()
        {
            m_PlayerCoTheClick = true;
        }

        private void TriggerCameraFocusAndDialogue()
        {
            Log("Triggering camera focus and dialogue...");

            // Tìm CameraFocusController
            CameraFocusController cameraFocus = FindObjectOfType<CameraFocusController>();
            if (cameraFocus != null)
            {
                Transform itemTransform = m_CurrentShopZone?.ItemDisplay?.transform;
                cameraFocus.FocusOnNPCAndItem(transform, itemTransform);
                Log("Camera focus triggered");
            }
            else
            {
                Log("⚠️ Không tìm thấy CameraFocusController");
            }

            // Tìm NPCDialogueSystem
            NPCDialogueSystem dialogueSystem = FindObjectOfType<NPCDialogueSystem>();
            if (dialogueSystem != null)
            {
                dialogueSystem.MoDialogue(this, m_CurrentShopZone);
                Log("Dialogue system triggered");
            }
            else
            {
                Log("⚠️ Không tìm thấy NPCDialogueSystem");
            }
        }

        private IEnumerator IdleBehavior()
        {
            yield return new WaitForSeconds(Random.Range(2f, 5f));
            ChuyenTrangThai(NPCState.Wandering);
        }

        private IEnumerator WanderingBehavior()
        {
            while (m_TrangThaiHienTai == NPCState.Wandering)
            {
                // Tìm shop zone gần nhất
                if (Time.time - m_LanTimShopCuoi >= m_ThoiGianChoTimShop)
                {
                    TimShopZoneGanNhat();
                    m_LanTimShopCuoi = Time.time;
                }
                
                // Di chuyển random
                Vector3 randomDirection = Random.insideUnitSphere * 10f;
                randomDirection += transform.position;
                randomDirection.y = transform.position.y;
                
                NavMeshHit hit;
                if (NavMesh.SamplePosition(randomDirection, out hit, 10f, NavMesh.AllAreas))
                {
                    m_Agent.SetDestination(hit.position);
                }
                
                yield return new WaitForSeconds(Random.Range(5f, 10f));
            }
        }

        private IEnumerator MovingToShopBehavior()
        {
            if (m_CurrentShopZone != null)
            {
                m_Agent.SetDestination(m_CurrentShopZone.ViTriNPCDung);
            }
            yield return null;
        }

        private IEnumerator LookingAtItemBehavior()
        {
            if (m_CurrentShopZone != null)
            {
                yield return new WaitForSeconds(m_CurrentShopZone.ThoiGianXemHang);
                ChuyenTrangThai(NPCState.WantToBuy);
            }
        }

        private IEnumerator WantToBuyBehavior()
        {
            if (m_CurrentShopZone != null && m_TextDisplay != null)
            {
                // Hiển thị "I want to buy..."
                m_TextDisplay.HienThiWantToBuy(m_CurrentShopZone.TenHienThi, m_CurrentShopZone.GiaBan);
                
                ChuyenTrangThai(NPCState.WaitingForPlayer);
                
                // Timeout sau một thời gian
                yield return new WaitForSeconds(m_ThoiGianChoPlayer);
                
                if (m_TrangThaiHienTai == NPCState.WaitingForPlayer)
                {
                    Log("Player không tương tác, quay về wandering");
                    if (m_TextDisplay != null)
                    {
                        m_TextDisplay.AnText();
                    }
                    ChuyenTrangThai(NPCState.Wandering);
                }
            }
        }

        private IEnumerator QuayVeTrangThaiWandering(float delay)
        {
            yield return new WaitForSeconds(delay);
            m_CurrentShopZone = null;
            m_PlayerCoTheClick = false;
            ChuyenTrangThai(NPCState.Wandering);
        }

        private void TimShopZoneGanNhat()
        {
            ShopDisplayZone[] shopZones = FindObjectsOfType<ShopDisplayZone>();
            Log($"Tìm thấy {shopZones.Length} shop zones trong scene");

            ShopDisplayZone shopGanNhat = null;
            float khoangCachGanNhat = float.MaxValue;

            foreach (var shop in shopZones)
            {
                float khoangCach = Vector3.Distance(transform.position, shop.transform.position);
                Log($"Shop {shop.TenHienThi}: khoảng cách {khoangCach:F1}m, giá {shop.GiaBan} Lea");

                // Kiểm tra có đủ tiền không
                bool duTien = shop.CoTheBanChoNPC(this);
                Log($"NPC có đủ tiền mua {shop.TenHienThi}: {duTien}");

                if (duTien)
                {
                    if (khoangCach < khoangCachGanNhat && khoangCach <= m_BanKinhTimKiem)
                    {
                        khoangCachGanNhat = khoangCach;
                        shopGanNhat = shop;
                        Log($"Chọn shop {shop.TenHienThi} làm target (khoảng cách: {khoangCach:F1}m)");
                    }
                    else if (khoangCach > m_BanKinhTimKiem)
                    {
                        Log($"Shop {shop.TenHienThi} quá xa (>{m_BanKinhTimKiem}m)");
                    }
                }
            }

            if (shopGanNhat != null)
            {
                Log($"Tìm thấy shop: {shopGanNhat.TenHienThi} - Di chuyển đến đó!");
                m_CurrentShopZone = shopGanNhat;
                ChuyenTrangThai(NPCState.MovingToShop);
            }
            else
            {
                Log("Không tìm thấy shop phù hợp hoặc không đủ tiền");
            }
        }

        private void Log(string message)
        {
            if (m_HienThiLog)
            {
                Debug.Log($"[NPCShoppingAI-{m_TenNPC}] {message}");
            }
        }
        #endregion

        #region Editor Methods
        private void OnValidate()
        {
            if (m_TocDoChuyenDong < 0) m_TocDoChuyenDong = 0;
            if (m_KhoangCachDung < 0) m_KhoangCachDung = 0;
            if (m_ThoiGianChoTimShop < 0) m_ThoiGianChoTimShop = 0;
            if (m_BanKinhTimKiem < 0) m_BanKinhTimKiem = 0;
            if (m_ThoiGianChoPlayer < 0) m_ThoiGianChoPlayer = 0;
        }
        #endregion
    }
}
