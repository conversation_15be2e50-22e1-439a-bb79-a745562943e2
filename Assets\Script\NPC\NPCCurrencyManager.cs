using UnityEngine;

namespace NPCSystem
{
    /// <summary>
    /// Quản lý tiền tệ cho NPC
    /// Tích hợp với hệ thống Economy hiện tại
    /// </summary>
    public class NPCCurrencyManager : MonoBehaviour
    {
        #region Serialized Fields
        [Header("💰 NPC Currency Settings")]
        [SerializeField, <PERSON><PERSON><PERSON>("Số tiền ban đầu của NPC")]
        private int m_SoTienBanDau = 1000;
        
        [SerializeField, Tooltip("Số tiền hiện tại của NPC")]
        private int m_SoTienHienTai = 0;
        
        [SerializeField, Toolt<PERSON>("Số tiền tối thiểu NPC cần giữ")]
        private int m_SoTienToiThieu = 100;
        
        [SerializeField, Tooltip("Hiển thị log debug")]
        private bool m_HienThiLog = true;
        
        [Header("🎯 Shopping Behavior")]
        [SerializeField, <PERSON>lt<PERSON>("Phần trăm tiền NPC sẵn sàng chi cho một món hàng (0-1)")]
        [Range(0f, 1f)]
        private float m_PhanTramChiTieu = 0.3f;
        
        [SerializeField, Tooltip("Thời gian cooldown giữa các lần mua (giây)")]
        private float m_ThoiGianCooldown = 30f;
        #endregion

        #region Private Fields
        private float m_LanMuaCuoiCung = 0f;
        #endregion

        #region Properties
        public int SoTienHienTai => m_SoTienHienTai;
        public int SoTienToiThieu => m_SoTienToiThieu;
        public bool CoTheMuaHang => Time.time - m_LanMuaCuoiCung >= m_ThoiGianCooldown;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            KhoiTaoTien();
        }

        private void Start()
        {
            Log($"NPC Currency Manager khởi tạo với {m_SoTienHienTai} Lea");
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Kiểm tra NPC có đủ tiền mua món hàng không
        /// </summary>
        public bool CoTheMua(int giaHang)
        {
            if (!CoTheMuaHang)
            {
                Log("NPC đang trong thời gian cooldown");
                return false;
            }

            int soTienCoTheChi = GetSoTienCoTheChi();
            bool duTien = giaHang <= soTienCoTheChi;
            
            Log($"Kiểm tra mua hàng: Giá {giaHang}, Có thể chi {soTienCoTheChi}, Kết quả: {duTien}");
            return duTien;
        }

        /// <summary>
        /// Thực hiện giao dịch mua hàng
        /// </summary>
        public bool ThucHienMua(int giaHang)
        {
            if (!CoTheMua(giaHang))
            {
                LogLoi($"Không thể mua hàng giá {giaHang} Lea");
                return false;
            }

            m_SoTienHienTai -= giaHang;
            m_LanMuaCuoiCung = Time.time;
            
            Log($"Đã mua hàng giá {giaHang} Lea. Còn lại: {m_SoTienHienTai} Lea");
            return true;
        }

        /// <summary>
        /// Thêm tiền cho NPC (có thể dùng cho quest reward, etc.)
        /// </summary>
        public void ThemTien(int soTien)
        {
            if (soTien <= 0) return;
            
            m_SoTienHienTai += soTien;
            Log($"Đã thêm {soTien} Lea. Tổng: {m_SoTienHienTai} Lea");
        }

        /// <summary>
        /// Lấy số tiền NPC có thể chi tiêu
        /// </summary>
        public int GetSoTienCoTheChi()
        {
            int soTienDuThua = m_SoTienHienTai - m_SoTienToiThieu;
            if (soTienDuThua <= 0) return 0;
            
            return Mathf.RoundToInt(soTienDuThua * m_PhanTramChiTieu);
        }

        /// <summary>
        /// Reset tiền về số tiền ban đầu
        /// </summary>
        [ContextMenu("Reset Tiền")]
        public void ResetTien()
        {
            m_SoTienHienTai = m_SoTienBanDau;
            m_LanMuaCuoiCung = 0f;
            Log($"Đã reset tiền về {m_SoTienHienTai} Lea");
        }
        #endregion

        #region Private Methods
        private void KhoiTaoTien()
        {
            if (m_SoTienHienTai <= 0)
            {
                m_SoTienHienTai = m_SoTienBanDau;
            }
        }

        private void Log(string message)
        {
            if (m_HienThiLog)
            {
                Debug.Log($"[NPCCurrency-{gameObject.name}] {message}");
            }
        }

        private void LogLoi(string message)
        {
            Debug.LogError($"[NPCCurrency-{gameObject.name}] {message}");
        }
        #endregion

        #region Editor Methods
        private void OnValidate()
        {
            if (m_SoTienBanDau < 0) m_SoTienBanDau = 0;
            if (m_SoTienToiThieu < 0) m_SoTienToiThieu = 0;
            if (m_ThoiGianCooldown < 0) m_ThoiGianCooldown = 0;
        }
        #endregion
    }
}
