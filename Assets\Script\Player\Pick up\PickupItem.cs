using UnityEngine;

namespace PlayerSystem
{
    /// <summary>
    /// Component cho các vật phẩm có thể pickup
    /// Chứa thông tin vật phẩm và xử lý pickup/drop logic
    /// </summary>
    public class PickupItem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("📦 Thông Tin Vật Phẩm")]
        [SerializeField, <PERSON>lt<PERSON>("ID của vật phẩm")]
        private string m_ItemId = "item";
        
        [SerializeField, Tooltip("Số lượng vật phẩm")]
        private int m_SoLuong = 1;
        
        [SerializeField, Tooltip("Tên hiển thị")]
        private string m_TenHienThi = "Vật Phẩm";
        
        [SerializeField, Tooltip("Mô tả vật phẩm")]
        private string m_MoTa = "Một vật phẩm có thể nhặt";

        [Header("🎮 Pickup Settings")]
        [SerializeField, <PERSON>ltip("<PERSON><PERSON> thể pickup không")]
        private bool m_CoThePickup = true;
        
        [Serial<PERSON><PERSON><PERSON>, Toolt<PERSON>("Tự động thêm vào inventory")]
        private bool m_TuDongThemVaoInventory = true;
        
        [SerializeField, Tooltip("Xóa object sau khi pickup")]
        private bool m_XoaSauKhiPickup = false;

        [Header("🎨 Visual Effects")]
        [SerializeField, Tooltip("Material để highlight")]
        private Material m_HighlightMaterial;
        
        [SerializeField, Tooltip("Màu outline khi highlight")]
        private Color m_OutlineColor = Color.yellow;
        
        [SerializeField, Tooltip("Độ dày outline")]
        private float m_OutlineWidth = 0.1f;

        [Header("🌊 Animation")]
        [SerializeField, Tooltip("Có bobbing animation")]
        private bool m_CoBobbing = true;
        
        [SerializeField, Tooltip("Tốc độ bobbing")]
        private float m_TocDoBobbing = 1f;
        
        [SerializeField, Tooltip("Độ cao bobbing")]
        private float m_DoCapBobbing = 0.1f;
        
        [SerializeField, Tooltip("Có rotation animation")]
        private bool m_CoRotation = false;
        
        [SerializeField, Tooltip("Tốc độ rotation")]
        private float m_TocDoRotation = 30f;

        [Header("🔊 Audio")]
        [SerializeField, Tooltip("Âm thanh khi highlight")]
        private AudioClip m_SoundHighlight;
        
        [SerializeField, Tooltip("Âm thanh khi pickup")]
        private AudioClip m_SoundPickup;
        
        [SerializeField, Tooltip("Âm thanh khi không thể pickup")]
        private AudioClip m_SoundCannotPickup;
        #endregion

        #region Private Fields
        private Renderer m_Renderer;
        private Material m_OriginalMaterial;
        private Vector3 m_ViTriBanDau;
        private bool m_DangHighlight = false;
        private AudioSource m_AudioSource;
        private Collider m_Collider;
        private Rigidbody m_Rigidbody;
        #endregion

        #region Properties
        /// <summary>ID của vật phẩm</summary>
        public string ItemId => m_ItemId;
        
        /// <summary>Số lượng vật phẩm</summary>
        public int SoLuong => m_SoLuong;
        
        /// <summary>Tên hiển thị</summary>
        public string TenHienThi => m_TenHienThi;
        
        /// <summary>Mô tả vật phẩm</summary>
        public string MoTa => m_MoTa;
        
        /// <summary>Có thể pickup không</summary>
        public bool CoThePickup => m_CoThePickup;
        
        /// <summary>Có đang được highlight không</summary>
        public bool DangHighlight => m_DangHighlight;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            KhoiTaoComponent();
        }

        private void Start()
        {
            LuuTrangThaiBanDau();
        }

        private void Update()
        {
            if (m_CoBobbing || m_CoRotation)
            {
                CapNhatAnimation();
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Thiết lập thông tin vật phẩm
        /// </summary>
        public void ThietLapVatPham(string itemId, int soLuong, string tenHienThi, string moTa)
        {
            m_ItemId = itemId;
            m_SoLuong = soLuong;
            m_TenHienThi = tenHienThi;
            m_MoTa = moTa;
        }

        /// <summary>
        /// Bật/tắt highlight cho vật phẩm
        /// </summary>
        public void Highlight(bool enable)
        {
            if (m_DangHighlight == enable) return;

            m_DangHighlight = enable;

            if (enable)
            {
                BatHighlight();
            }
            else
            {
                TatHighlight();
            }
        }

        /// <summary>
        /// Thử pickup vật phẩm
        /// </summary>
        public bool TryPickup(object inventory)
        {
            if (!m_CoThePickup)
            {
                PhatAmThanh(m_SoundCannotPickup);
                return false;
            }

            // Xử lý pickup
            if (XuLyPickup(inventory))
            {
                PhatAmThanh(m_SoundPickup);
                
                if (m_XoaSauKhiPickup)
                {
                    Destroy(gameObject);
                }
                else
                {
                    // Ẩn object thay vì xóa
                    gameObject.SetActive(false);
                }
                
                return true;
            }

            return false;
        }

        /// <summary>
        /// Thả vật phẩm tại vị trí chỉ định
        /// </summary>
        public void Drop(Vector3 position)
        {
            transform.position = position;
            transform.SetParent(null);
            
            // Kích hoạt lại physics
            if (m_Rigidbody != null)
            {
                m_Rigidbody.isKinematic = false;
            }
            
            if (m_Collider != null)
            {
                m_Collider.enabled = true;
            }
            
            gameObject.SetActive(true);
            m_CoThePickup = true;
        }

        /// <summary>
        /// Thiết lập có thể pickup hay không
        /// </summary>
        public void SetCoThePickup(bool coThePickup)
        {
            m_CoThePickup = coThePickup;
        }
        #endregion

        #region Private Methods
        private void KhoiTaoComponent()
        {
            m_Renderer = GetComponent<Renderer>();
            m_AudioSource = GetComponent<AudioSource>();
            m_Collider = GetComponent<Collider>();
            m_Rigidbody = GetComponent<Rigidbody>();

            // Tạo AudioSource nếu chưa có
            if (m_AudioSource == null)
            {
                m_AudioSource = gameObject.AddComponent<AudioSource>();
                m_AudioSource.playOnAwake = false;
                m_AudioSource.spatialBlend = 1f; // 3D sound
            }

            // Đảm bảo có Collider
            if (m_Collider == null)
            {
                m_Collider = gameObject.AddComponent<BoxCollider>();
            }
        }

        private void LuuTrangThaiBanDau()
        {
            m_ViTriBanDau = transform.position;
            
            if (m_Renderer != null)
            {
                m_OriginalMaterial = m_Renderer.material;
            }
        }

        private void CapNhatAnimation()
        {
            float time = Time.time;

            // Bobbing animation
            if (m_CoBobbing)
            {
                float bobbingOffset = Mathf.Sin(time * m_TocDoBobbing) * m_DoCapBobbing;
                Vector3 newPosition = m_ViTriBanDau + Vector3.up * bobbingOffset;
                transform.position = newPosition;
            }

            // Rotation animation
            if (m_CoRotation)
            {
                transform.Rotate(Vector3.up, m_TocDoRotation * Time.deltaTime);
            }
        }

        private void BatHighlight()
        {
            if (m_Renderer == null) return;

            // Sử dụng highlight material nếu có
            if (m_HighlightMaterial != null)
            {
                m_Renderer.material = m_HighlightMaterial;
            }
            else
            {
                // Tạo highlight effect đơn giản
                Material highlightMat = new Material(m_OriginalMaterial);
                highlightMat.color = m_OutlineColor;
                highlightMat.SetFloat("_Metallic", 0.5f);
                highlightMat.SetFloat("_Smoothness", 0.8f);
                m_Renderer.material = highlightMat;
            }

            // Phát âm thanh highlight
            PhatAmThanh(m_SoundHighlight);
        }

        private void TatHighlight()
        {
            if (m_Renderer != null && m_OriginalMaterial != null)
            {
                m_Renderer.material = m_OriginalMaterial;
            }
        }

        private bool XuLyPickup(object inventory)
        {
            // TODO: Tích hợp với inventory system
            // Hiện tại chỉ return true để test
            
            if (m_TuDongThemVaoInventory && inventory != null)
            {
                // Sẽ tích hợp với PlayerInventory sau
                // return inventory.AddItem(m_ItemId, m_SoLuong);
            }

            return true;
        }

        private void PhatAmThanh(AudioClip clip)
        {
            if (m_AudioSource != null && clip != null)
            {
                m_AudioSource.PlayOneShot(clip);
            }
        }
        #endregion

        #region Debug
        private void OnDrawGizmosSelected()
        {
            // Vẽ thông tin debug
            Gizmos.color = m_CoThePickup ? Color.green : Color.red;
            Gizmos.DrawWireCube(transform.position, Vector3.one * 0.1f);
            
            if (m_CoBobbing)
            {
                Gizmos.color = Color.blue;
                Gizmos.DrawLine(m_ViTriBanDau + Vector3.up * m_DoCapBobbing, 
                               m_ViTriBanDau - Vector3.up * m_DoCapBobbing);
            }
        }
        #endregion
    }
}
