using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// H<PERSON> thống pickup đơn giản - chỉ nhặt và thả vật
/// Nhấn E để nhặt, nhấn E lại để thả
/// </summary>
public class SimplePickup : MonoBehaviour
{
    [Header("🎮 Pickup Settings")]
    [SerializeField] private float m_PickupRange = 3f;
    [SerializeField] private KeyCode m_PickupKey = KeyCode.E;
    [SerializeField] private LayerMask m_PickupLayer = -1;
    [SerializeField] private string m_PickupTag = "Pickupable";

    [Header("📦 Carry Settings")]
    [SerializeField] private Transform m_CarryPosition;
    [SerializeField, Tooltip("Forward distance from camera")]
    private float m_CarryDistance = 1.5f;
    [SerializeField, Tooltip("Height offset from camera center")]
    private float m_CarryHeight = -0.3f;
    [SerializeField, Tooltip("Side offset (positive = right, negative = left)")]
    private float m_CarrySideOffset = 0.3f;
    [SerializeField, Toolt<PERSON>("Update carry position in real-time")]
    private bool m_UpdatePositionInRealTime = true;

    [Header("🎨 UI Settings")]
    [SerializeField] private GameObject m_HoverTextUI;
    [SerializeField] private TextMeshProUGUI m_HoverText;
    [SerializeField] private string m_PickupText = "Press E to pick up";
    [SerializeField] private string m_DropText = "Press E to drop";

    [Header("🎯 Drop Settings")]
    [SerializeField, Tooltip("Khoảng cách thả vật phía trước")]
    private float m_DropDistance = 1.5f;

    [SerializeField, Tooltip("Độ cao thả vật so với mặt đất")]
    private float m_DropHeight = 0.5f;

    [SerializeField, Tooltip("Kiểm tra va chạm khi thả")]
    private bool m_CheckDropCollision = true;

    [Header("🔧 References")]
    [SerializeField] private Camera m_PlayerCamera;

    private GameObject m_CarriedObject;
    private Rigidbody m_CarriedRigidbody;
    private bool m_IsCarrying = false;
    private GameObject m_CurrentLookingAt;

    // Store previous values to detect changes in inspector
    private float m_PreviousCarryDistance;
    private float m_PreviousCarryHeight;
    private float m_PreviousCarrySideOffset;
    private float m_PreviousDropDistance;
    private float m_PreviousDropHeight;
    
    void Start()
    {
        // Tự động tìm camera nếu chưa có
        if (m_PlayerCamera == null)
            m_PlayerCamera = Camera.main;

        // Tạo carry position nếu chưa có
        if (m_CarryPosition == null)
        {
            CreateCarryPosition();
        }
        else
        {
            // Cập nhật vị trí carry position với settings hiện tại
            UpdateCarryPosition();
        }

        // Tạo hover UI nếu chưa có
        CreateHoverUI();

        // Store initial values
        StorePreviousValues();
    }

    #if UNITY_EDITOR
    void OnValidate()
    {
        // Update carry position when inspector values change
        if (HasValuesChanged())
        {
            UpdateCarryPosition();
            StorePreviousValues();
        }
    }
    #endif
    
    void Update()
    {
        // Kiểm tra hover
        CheckHover();

        if (Input.GetKeyDown(m_PickupKey))
        {
            if (m_IsCarrying)
            {
                DropObject();
            }
            else
            {
                TryPickupObject();
            }
        }

        // Cập nhật vị trí carry position trong real-time (chỉ trong Editor)
        #if UNITY_EDITOR
        if (m_UpdatePositionInRealTime && m_CarryPosition != null)
        {
            UpdateCarryPosition();
        }
        #endif

        // Giữ vật ở vị trí cố định
        if (m_IsCarrying && m_CarriedObject != null)
        {
            KeepObjectInPosition();
        }
    }
    
    void TryPickupObject()
    {
        if (m_PlayerCamera == null) return;
        
        // Raycast để tìm vật có thể nhặt
        RaycastHit hit;
        Vector3 rayOrigin = m_PlayerCamera.transform.position;
        Vector3 rayDirection = m_PlayerCamera.transform.forward;
        
        if (Physics.Raycast(rayOrigin, rayDirection, out hit, m_PickupRange, m_PickupLayer))
        {
            GameObject hitObject = hit.collider.gameObject;
            
            // Kiểm tra tag
            if (hitObject.CompareTag(m_PickupTag))
            {
                PickupObject(hitObject);
            }
        }
    }
    
    void PickupObject(GameObject obj)
    {
        m_CarriedObject = obj;
        m_CarriedRigidbody = obj.GetComponent<Rigidbody>();
        m_IsCarrying = true;

        // Tắt physics của vật
        if (m_CarriedRigidbody != null)
        {
            m_CarriedRigidbody.isKinematic = true;
            m_CarriedRigidbody.useGravity = false;
        }

        // Tắt collider để không va chạm với player
        Collider objCollider = obj.GetComponent<Collider>();
        if (objCollider != null)
        {
            objCollider.isTrigger = true; // Đổi thành trigger thay vì tắt hoàn toàn
        }

        // Đặt vật làm con của carry position để giữ chặt
        obj.transform.SetParent(m_CarryPosition);
        obj.transform.localPosition = Vector3.zero;
        obj.transform.localRotation = Quaternion.identity;

        // Ẩn hover text
        ShowHoverText(false);

        Debug.Log($"Đã nhặt: {obj.name}");
    }
    
    void DropObject()
    {
        if (m_CarriedObject == null) return;

        // Tách vật ra khỏi carry position
        m_CarriedObject.transform.SetParent(null);

        // Tính vị trí thả tốt hơn
        Vector3 dropPosition = CalculateDropPosition();
        m_CarriedObject.transform.position = dropPosition;

        // Bật lại physics
        if (m_CarriedRigidbody != null)
        {
            m_CarriedRigidbody.isKinematic = false;
            m_CarriedRigidbody.useGravity = true;

            // Thêm một chút force nhẹ theo hướng camera
            if (m_PlayerCamera != null)
            {
                Vector3 cameraForward = m_PlayerCamera.transform.forward;
                Vector3 horizontalForward = new Vector3(cameraForward.x, 0f, cameraForward.z).normalized;
                Vector3 dropForce = horizontalForward * 2f + Vector3.up * 1f;
                m_CarriedRigidbody.AddForce(dropForce, ForceMode.Impulse);
            }
            else
            {
                // Fallback nếu không có camera
                Vector3 dropForce = transform.forward * 2f + Vector3.up * 1f;
                m_CarriedRigidbody.AddForce(dropForce, ForceMode.Impulse);
            }
        }

        // Bật lại collider
        Collider objCollider = m_CarriedObject.GetComponent<Collider>();
        if (objCollider != null)
        {
            objCollider.isTrigger = false;
        }

        Debug.Log($"Đã thả: {m_CarriedObject.name} tại {dropPosition}");

        m_CarriedObject = null;
        m_CarriedRigidbody = null;
        m_IsCarrying = false;
    }

    Vector3 CalculateDropPosition()
    {
        if (m_PlayerCamera == null)
        {
            // Fallback nếu không có camera
            Vector3 playerPosition = transform.position;
            Vector3 playerForward = transform.forward;
            return playerPosition + playerForward * m_DropDistance + Vector3.up * m_DropHeight;
        }

        // Sử dụng vị trí và hướng của camera thay vì player transform
        Vector3 cameraPosition = m_PlayerCamera.transform.position;
        Vector3 cameraForward = m_PlayerCamera.transform.forward;

        // Tính vị trí thả dựa trên camera (loại bỏ component Y để chỉ lấy hướng ngang)
        Vector3 horizontalForward = new Vector3(cameraForward.x, 0f, cameraForward.z).normalized;

        // Vị trí thả cơ bản (phía trước theo hướng camera nhìn)
        Vector3 baseDropPosition = cameraPosition + horizontalForward * m_DropDistance;

        if (!m_CheckDropCollision)
        {
            // Không kiểm tra va chạm, chỉ thả ở độ cao mong muốn
            baseDropPosition.y = cameraPosition.y + m_DropHeight;
            return baseDropPosition;
        }

        // Kiểm tra va chạm với tường/vật cản từ camera
        RaycastHit wallHit;
        if (Physics.Raycast(cameraPosition, horizontalForward, out wallHit, m_DropDistance + 0.5f))
        {
            // Có tường, thả gần hơn
            baseDropPosition = wallHit.point - horizontalForward * 0.5f;
        }

        // Kiểm tra mặt đất để thả ở độ cao phù hợp
        RaycastHit groundHit;
        Vector3 rayStart = baseDropPosition + Vector3.up * 3f; // Bắt đầu từ cao hơn

        if (Physics.Raycast(rayStart, Vector3.down, out groundHit, 6f))
        {
            // Tìm thấy mặt đất, thả ở độ cao mong muốn so với mặt đất
            baseDropPosition.y = groundHit.point.y + m_DropHeight;
        }
        else
        {
            // Không tìm thấy mặt đất, dùng độ cao camera
            baseDropPosition.y = cameraPosition.y + m_DropHeight;
        }

        return baseDropPosition;
    }
    
    void KeepObjectInPosition()
    {
        // Vật đã được set parent nên sẽ tự động theo camera
        // Không cần code gì thêm, vật sẽ giữ nguyên vị trí relative với camera
    }

    void CheckHover()
    {
        if (m_IsCarrying)
        {
            // Hiển thị text drop khi đang cầm
            ShowHoverText(true, m_DropText);
            return;
        }

        // Raycast để kiểm tra hover
        if (m_PlayerCamera == null) return;

        RaycastHit hit;
        Vector3 rayOrigin = m_PlayerCamera.transform.position;
        Vector3 rayDirection = m_PlayerCamera.transform.forward;

        if (Physics.Raycast(rayOrigin, rayDirection, out hit, m_PickupRange, m_PickupLayer))
        {
            GameObject hitObject = hit.collider.gameObject;

            if (hitObject.CompareTag(m_PickupTag))
            {
                if (m_CurrentLookingAt != hitObject)
                {
                    m_CurrentLookingAt = hitObject;
                    ShowHoverText(true, m_PickupText);
                }
                return;
            }
        }

        // Không nhìn vào vật nào
        if (m_CurrentLookingAt != null)
        {
            m_CurrentLookingAt = null;
            ShowHoverText(false);
        }
    }
    
    void CreateHoverUI()
    {
        if (m_HoverTextUI != null) return;

        // Tìm Canvas
        Canvas canvas = FindObjectOfType<Canvas>();
        if (canvas == null)
        {
            // Tạo Canvas mới
            GameObject canvasObj = new GameObject("PickupCanvas");
            canvas = canvasObj.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasObj.AddComponent<UnityEngine.UI.CanvasScaler>();
            canvasObj.AddComponent<UnityEngine.UI.GraphicRaycaster>();
        }

        // Tạo hover text UI
        m_HoverTextUI = new GameObject("HoverText");
        m_HoverTextUI.transform.SetParent(canvas.transform, false);

        RectTransform rectTransform = m_HoverTextUI.AddComponent<RectTransform>();
        rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
        rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
        rectTransform.anchoredPosition = new Vector2(0, 50);
        rectTransform.sizeDelta = new Vector2(200, 50);

        // Thêm text component
        m_HoverText = m_HoverTextUI.AddComponent<TextMeshProUGUI>();
        m_HoverText.text = m_PickupText;
        m_HoverText.fontSize = 16;
        m_HoverText.color = Color.white;
        m_HoverText.alignment = TextAlignmentOptions.Center;

        // Ẩn ban đầu
        m_HoverTextUI.SetActive(false);
    }

    void ShowHoverText(bool show, string text = "")
    {
        if (m_HoverTextUI == null) return;

        m_HoverTextUI.SetActive(show);

        if (show && !string.IsNullOrEmpty(text) && m_HoverText != null)
        {
            m_HoverText.text = text;
        }
    }

    void CreateCarryPosition()
    {
        GameObject carryPos = new GameObject("CarryPosition");
        carryPos.transform.SetParent(m_PlayerCamera.transform);
        m_CarryPosition = carryPos.transform;
        UpdateCarryPosition();
    }

    void UpdateCarryPosition()
    {
        if (m_CarryPosition == null || m_PlayerCamera == null) return;

        // Tính toán vị trí local dựa trên settings
        Vector3 localPosition = new Vector3(
            m_CarrySideOffset,      // X: side offset (right/left)
            m_CarryHeight,          // Y: height offset
            m_CarryDistance         // Z: forward distance
        );

        m_CarryPosition.localPosition = localPosition;
    }

    /// <summary>
    /// Update carry position manually (useful in Editor)
    /// </summary>
    [ContextMenu("Update Carry Position")]
    public void UpdateCarryPositionManual()
    {
        if (m_CarryPosition == null)
        {
            CreateCarryPosition();
        }
        else
        {
            UpdateCarryPosition();
        }

        #if UNITY_EDITOR
        UnityEditor.EditorUtility.SetDirty(this);
        #endif
    }

    /// <summary>
    /// Test drop position (for debugging)
    /// </summary>
    [ContextMenu("Test Drop Position")]
    public void TestDropPosition()
    {
        Vector3 dropPos = CalculateDropPosition();
        Debug.Log($"🎯 Drop Position: {dropPos}");

        if (m_PlayerCamera != null)
        {
            Vector3 cameraPos = m_PlayerCamera.transform.position;
            Vector3 cameraForward = m_PlayerCamera.transform.forward;
            Debug.Log($"📷 Camera Position: {cameraPos}");
            Debug.Log($"📷 Camera Forward: {cameraForward}");
            Debug.Log($"📏 Distance from camera: {Vector3.Distance(cameraPos, dropPos):F2}m");
        }

        // Tạo marker tạm thời để visualize
        #if UNITY_EDITOR
        GameObject marker = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        marker.name = "DROP_MARKER_TEMP";
        marker.transform.position = dropPos;
        marker.transform.localScale = Vector3.one * 0.2f;

        var renderer = marker.GetComponent<Renderer>();
        if (renderer != null)
        {
            renderer.material.color = Color.red;
        }

        // Xóa marker sau 3 giây
        if (Application.isPlaying)
        {
            Destroy(marker, 3f);
        }
        #endif
    }

    void StorePreviousValues()
    {
        m_PreviousCarryDistance = m_CarryDistance;
        m_PreviousCarryHeight = m_CarryHeight;
        m_PreviousCarrySideOffset = m_CarrySideOffset;
        m_PreviousDropDistance = m_DropDistance;
        m_PreviousDropHeight = m_DropHeight;
    }

    bool HasValuesChanged()
    {
        return m_PreviousCarryDistance != m_CarryDistance ||
               m_PreviousCarryHeight != m_CarryHeight ||
               m_PreviousCarrySideOffset != m_CarrySideOffset ||
               m_PreviousDropDistance != m_DropDistance ||
               m_PreviousDropHeight != m_DropHeight;
    }

    // Hiển thị gizmos để debug
    void OnDrawGizmosSelected()
    {
        if (m_PlayerCamera != null)
        {
            // Vẽ pickup range
            Gizmos.color = Color.yellow;
            Gizmos.DrawRay(m_PlayerCamera.transform.position, m_PlayerCamera.transform.forward * m_PickupRange);
        }

        if (m_CarryPosition != null)
        {
            // Vẽ carry position
            Gizmos.color = m_IsCarrying ? Color.red : Color.green;
            Gizmos.DrawWireSphere(m_CarryPosition.position, 0.15f);

            // Vẽ line từ camera đến carry position
            if (m_PlayerCamera != null)
            {
                Gizmos.color = Color.cyan;
                Gizmos.DrawLine(m_PlayerCamera.transform.position, m_CarryPosition.position);
            }
        }

        // Vẽ preview carry position dựa trên inspector values (chỉ trong editor)
        #if UNITY_EDITOR
        if (m_PlayerCamera != null && !Application.isPlaying)
        {
            Vector3 previewPosition = m_PlayerCamera.transform.position +
                                    m_PlayerCamera.transform.right * m_CarrySideOffset +
                                    m_PlayerCamera.transform.up * m_CarryHeight +
                                    m_PlayerCamera.transform.forward * m_CarryDistance;

            Gizmos.color = Color.blue;
            Gizmos.DrawWireCube(previewPosition, Vector3.one * 0.1f);

            // Vẽ preview drop position theo hướng camera
            Vector3 cameraForward = m_PlayerCamera.transform.forward;
            Vector3 horizontalForward = new Vector3(cameraForward.x, 0f, cameraForward.z).normalized;
            Vector3 dropPreview = m_PlayerCamera.transform.position + horizontalForward * m_DropDistance;
            dropPreview.y = m_PlayerCamera.transform.position.y + m_DropHeight;

            Gizmos.color = Color.magenta;
            Gizmos.DrawWireCube(dropPreview, Vector3.one * 0.15f);

            // Vẽ line từ camera đến drop position
            Gizmos.color = Color.red;
            Gizmos.DrawLine(m_PlayerCamera.transform.position, dropPreview);

            // Vẽ arrow để hiển thị hướng drop
            Vector3 arrowEnd = dropPreview + horizontalForward * 0.3f;
            Gizmos.DrawLine(dropPreview, arrowEnd);

            // Vẽ text "DROP" (chỉ trong Scene view)
            #if UNITY_EDITOR
            UnityEditor.Handles.Label(dropPreview + Vector3.up * 0.3f, "DROP");
            #endif
        }
        #endif
    }
}
