using UnityEngine;
using UnityEngine.UI;
using T<PERSON><PERSON>;

namespace PlayerSystem
{
    /// <summary>
    /// UI hiển thị thông tin pickup và prompts
    /// Hỗ trợ hover text và hướng dẫn tương tác
    /// </summary>
    public class PickupUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎨 UI Components")]
        [SerializeField, <PERSON>lt<PERSON>("Panel chính chứa UI")]
        private GameObject m_MainPanel;
        
        [SerializeField, Tooltip("Text hiển thị tên vật phẩm")]
        private TextMeshProUGUI m_TextTenVatPham;
        
        [Serial<PERSON><PERSON><PERSON>, Toolt<PERSON>("Text hiển thị số lượng")]
        private TextMeshProUGUI m_TextSoLuong;
        
        [SerializeField, Tooltip("Text hiển thị mô tả")]
        private TextMeshProUGUI m_TextMoTa;
        
        [SerializeField, Tooltip("Text hiển thị hướng dẫn")]
        private TextMeshProUGUI m_TextHuongDan;
        
        [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Icon vật phẩm (tùy chọn)")]
        private Image m_IconVatPham;
        
        [SerializeField, Tooltip("Progress bar (tùy chọn)")]
        private Slider m_ProgressBar;

        [Header("🎯 Crosshair")]
        [SerializeField, Tooltip("Crosshair bình thường")]
        private GameObject m_CrosshairNormal;
        
        [SerializeField, Tooltip("Crosshair khi có thể pickup")]
        private GameObject m_CrosshairPickup;
        
        [SerializeField, Tooltip("Crosshair khi đang mang vật")]
        private GameObject m_CrosshairCarrying;

        [Header("⚙️ Settings")]
        [SerializeField, Tooltip("Tốc độ fade in/out")]
        private float m_TocDoFade = 5f;
        
        [SerializeField, Tooltip("Có scale animation")]
        private bool m_CoScaleAnimation = true;
        
        [SerializeField, Tooltip("Scale ban đầu")]
        private float m_ScaleBanDau = 0.8f;
        
        [SerializeField, Tooltip("Thời gian scale")]
        private float m_ThoiGianScale = 0.3f;
        
        [SerializeField, Tooltip("Offset từ center màn hình")]
        private Vector2 m_OffsetTuCenter = new Vector2(0, 100);

        [Header("🎨 Styling")]
        [SerializeField, Tooltip("Màu text tên vật phẩm")]
        private Color m_MauTextTen = Color.white;
        
        [SerializeField, Tooltip("Màu text hướng dẫn")]
        private Color m_MauTextHuongDan = Color.yellow;
        
        [SerializeField, Tooltip("Màu background panel")]
        private Color m_MauBackground = new Color(0, 0, 0, 0.7f);
        #endregion

        #region Private Fields
        private CanvasGroup m_CanvasGroup;
        private RectTransform m_RectTransform;
        private bool m_DangHienThi = false;
        private PickupItem m_ItemHienTai;
        private Coroutine m_CoroutineAnimation;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            KhoiTaoComponent();
        }

        private void Start()
        {
            ThietLapUI();
            AnPrompt();
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Hiển thị prompt cho vật phẩm
        /// </summary>
        public void HienThiPrompt(PickupItem item)
        {
            if (item == null) return;

            m_ItemHienTai = item;
            CapNhatNoiDung(item);
            
            if (!m_DangHienThi)
            {
                m_DangHienThi = true;
                HienThiPanel();
            }
            
            CapNhatCrosshair(CrosshairState.CanPickup);
        }

        /// <summary>
        /// Ẩn prompt
        /// </summary>
        public void AnPrompt()
        {
            if (m_DangHienThi)
            {
                m_DangHienThi = false;
                AnPanel();
            }
            
            m_ItemHienTai = null;
            CapNhatCrosshair(CrosshairState.Normal);
        }

        /// <summary>
        /// Cập nhật trạng thái carrying
        /// </summary>
        public void CapNhatTrangThaiCarrying(bool isCarrying)
        {
            if (isCarrying)
            {
                CapNhatCrosshair(CrosshairState.Carrying);
                
                // Hiển thị hướng dẫn thả
                if (m_TextHuongDan != null)
                {
                    m_TextHuongDan.text = "Nhấn [E] để thả";
                }
            }
            else
            {
                CapNhatCrosshair(CrosshairState.Normal);
            }
        }

        /// <summary>
        /// Thiết lập UI position
        /// </summary>
        public void ThietLapViTri(Vector2 screenPosition)
        {
            if (m_RectTransform != null)
            {
                m_RectTransform.position = screenPosition + m_OffsetTuCenter;
            }
        }
        #endregion

        #region Private Methods
        private void KhoiTaoComponent()
        {
            m_CanvasGroup = GetComponent<CanvasGroup>();
            if (m_CanvasGroup == null)
            {
                m_CanvasGroup = gameObject.AddComponent<CanvasGroup>();
            }

            m_RectTransform = GetComponent<RectTransform>();
            
            // Tìm main panel nếu chưa có
            if (m_MainPanel == null)
            {
                m_MainPanel = transform.GetChild(0)?.gameObject;
            }
        }

        private void ThietLapUI()
        {
            // Thiết lập màu sắc
            if (m_TextTenVatPham != null)
            {
                m_TextTenVatPham.color = m_MauTextTen;
            }
            
            if (m_TextHuongDan != null)
            {
                m_TextHuongDan.color = m_MauTextHuongDan;
            }

            // Thiết lập background
            if (m_MainPanel != null)
            {
                Image background = m_MainPanel.GetComponent<Image>();
                if (background != null)
                {
                    background.color = m_MauBackground;
                }
            }

            // Thiết lập vị trí ban đầu
            if (m_RectTransform != null)
            {
                m_RectTransform.anchorMin = new Vector2(0.5f, 0.5f);
                m_RectTransform.anchorMax = new Vector2(0.5f, 0.5f);
                m_RectTransform.anchoredPosition = m_OffsetTuCenter;
            }
        }

        private void CapNhatNoiDung(PickupItem item)
        {
            // Cập nhật tên vật phẩm
            if (m_TextTenVatPham != null)
            {
                m_TextTenVatPham.text = item.TenHienThi;
            }

            // Cập nhật số lượng
            if (m_TextSoLuong != null)
            {
                if (item.SoLuong > 1)
                {
                    m_TextSoLuong.text = $"x{item.SoLuong}";
                    m_TextSoLuong.gameObject.SetActive(true);
                }
                else
                {
                    m_TextSoLuong.gameObject.SetActive(false);
                }
            }

            // Cập nhật mô tả
            if (m_TextMoTa != null)
            {
                m_TextMoTa.text = item.MoTa;
                m_TextMoTa.gameObject.SetActive(!string.IsNullOrEmpty(item.MoTa));
            }

            // Cập nhật hướng dẫn
            if (m_TextHuongDan != null)
            {
                m_TextHuongDan.text = "Nhấn [E] để nhặt";
            }

            // TODO: Cập nhật icon nếu có
            if (m_IconVatPham != null)
            {
                // Sẽ tích hợp với item icon system sau
                m_IconVatPham.gameObject.SetActive(false);
            }
        }

        private void HienThiPanel()
        {
            if (m_MainPanel != null)
            {
                m_MainPanel.SetActive(true);
            }

            // Animation fade in
            if (m_CoroutineAnimation != null)
            {
                StopCoroutine(m_CoroutineAnimation);
            }
            
            m_CoroutineAnimation = StartCoroutine(AnimationFadeIn());
        }

        private void AnPanel()
        {
            // Animation fade out
            if (m_CoroutineAnimation != null)
            {
                StopCoroutine(m_CoroutineAnimation);
            }
            
            m_CoroutineAnimation = StartCoroutine(AnimationFadeOut());
        }

        private System.Collections.IEnumerator AnimationFadeIn()
        {
            float timer = 0f;
            float startAlpha = m_CanvasGroup.alpha;
            float startScale = m_CoScaleAnimation ? m_ScaleBanDau : 1f;

            if (m_CoScaleAnimation)
            {
                transform.localScale = Vector3.one * startScale;
            }

            while (timer < m_ThoiGianScale)
            {
                timer += Time.deltaTime;
                float progress = timer / m_ThoiGianScale;
                
                // Fade alpha
                m_CanvasGroup.alpha = Mathf.Lerp(startAlpha, 1f, progress);
                
                // Scale animation
                if (m_CoScaleAnimation)
                {
                    float scale = Mathf.Lerp(startScale, 1f, progress);
                    transform.localScale = Vector3.one * scale;
                }

                yield return null;
            }

            m_CanvasGroup.alpha = 1f;
            if (m_CoScaleAnimation)
            {
                transform.localScale = Vector3.one;
            }
        }

        private System.Collections.IEnumerator AnimationFadeOut()
        {
            float timer = 0f;
            float startAlpha = m_CanvasGroup.alpha;
            float startScale = 1f;

            while (timer < m_ThoiGianScale)
            {
                timer += Time.deltaTime;
                float progress = timer / m_ThoiGianScale;
                
                // Fade alpha
                m_CanvasGroup.alpha = Mathf.Lerp(startAlpha, 0f, progress);
                
                // Scale animation
                if (m_CoScaleAnimation)
                {
                    float scale = Mathf.Lerp(startScale, m_ScaleBanDau, progress);
                    transform.localScale = Vector3.one * scale;
                }

                yield return null;
            }

            m_CanvasGroup.alpha = 0f;
            if (m_MainPanel != null)
            {
                m_MainPanel.SetActive(false);
            }
        }

        private void CapNhatCrosshair(CrosshairState state)
        {
            // Tắt tất cả crosshair
            if (m_CrosshairNormal != null) m_CrosshairNormal.SetActive(false);
            if (m_CrosshairPickup != null) m_CrosshairPickup.SetActive(false);
            if (m_CrosshairCarrying != null) m_CrosshairCarrying.SetActive(false);

            // Bật crosshair tương ứng
            switch (state)
            {
                case CrosshairState.Normal:
                    if (m_CrosshairNormal != null) m_CrosshairNormal.SetActive(true);
                    break;
                case CrosshairState.CanPickup:
                    if (m_CrosshairPickup != null) m_CrosshairPickup.SetActive(true);
                    break;
                case CrosshairState.Carrying:
                    if (m_CrosshairCarrying != null) m_CrosshairCarrying.SetActive(true);
                    break;
            }
        }
        #endregion

        #region Enums
        private enum CrosshairState
        {
            Normal,
            CanPickup,
            Carrying
        }
        #endregion
    }
}
