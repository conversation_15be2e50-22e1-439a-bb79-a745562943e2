{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 21928, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 21928, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 21928, "tid": 803115, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 21928, "tid": 803115, "ts": 1750153769134183, "dur": 695, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 21928, "tid": 803115, "ts": 1750153769138260, "dur": 801, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 21928, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 21928, "tid": 1, "ts": 1750153745260398, "dur": 26374, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21928, "tid": 1, "ts": 1750153745286777, "dur": 64605, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21928, "tid": 1, "ts": 1750153745351394, "dur": 39822, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 21928, "tid": 803115, "ts": 1750153769139065, "dur": 14, "ph": "X", "name": "", "args": {}}, {"pid": 21928, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745258361, "dur": 10357, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745268721, "dur": 23857259, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745269713, "dur": 2349, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745272071, "dur": 454, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745272527, "dur": 12731, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745285268, "dur": 216, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745285487, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745285549, "dur": 787, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745286338, "dur": 18415, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745304765, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745304770, "dur": 646, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745305420, "dur": 585, "ph": "X", "name": "ProcessMessages 1890", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306007, "dur": 198, "ph": "X", "name": "ReadAsync 1890", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306207, "dur": 10, "ph": "X", "name": "ProcessMessages 20491", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306219, "dur": 33, "ph": "X", "name": "ReadAsync 20491", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306255, "dur": 1, "ph": "X", "name": "ProcessMessages 1099", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306258, "dur": 26, "ph": "X", "name": "ReadAsync 1099", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306285, "dur": 2, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306288, "dur": 24, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306315, "dur": 22, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306339, "dur": 26, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306368, "dur": 27, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306399, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306401, "dur": 28, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306432, "dur": 20, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306454, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306477, "dur": 21, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306500, "dur": 25, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306528, "dur": 19, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306549, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306568, "dur": 35, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306606, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306608, "dur": 54, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306665, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306668, "dur": 42, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306712, "dur": 1, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306714, "dur": 24, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306741, "dur": 25, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306768, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306769, "dur": 22, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306794, "dur": 22, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306818, "dur": 20, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306841, "dur": 23, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306867, "dur": 22, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306891, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306892, "dur": 38, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306936, "dur": 2, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745306939, "dur": 71, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307015, "dur": 3, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307020, "dur": 49, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307070, "dur": 2, "ph": "X", "name": "ProcessMessages 1391", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307073, "dur": 23, "ph": "X", "name": "ReadAsync 1391", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307099, "dur": 21, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307124, "dur": 23, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307150, "dur": 75, "ph": "X", "name": "ReadAsync 27", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307231, "dur": 48, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307280, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307282, "dur": 38, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307323, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307325, "dur": 46, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307376, "dur": 2, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307380, "dur": 31, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307411, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307414, "dur": 46, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307464, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307495, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307497, "dur": 25, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307525, "dur": 29, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307556, "dur": 21, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307579, "dur": 20, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307603, "dur": 28, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307634, "dur": 35, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307672, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307673, "dur": 23, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307700, "dur": 54, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307756, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307790, "dur": 11, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307803, "dur": 18, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307823, "dur": 17, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307842, "dur": 17, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307861, "dur": 23, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307888, "dur": 25, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307917, "dur": 27, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307947, "dur": 23, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307972, "dur": 25, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745307999, "dur": 22, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308024, "dur": 19, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308046, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308068, "dur": 29, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308100, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308101, "dur": 27, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308131, "dur": 22, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308155, "dur": 19, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308178, "dur": 22, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308202, "dur": 15, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308220, "dur": 18, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308240, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308263, "dur": 23, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308288, "dur": 17, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308307, "dur": 18, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308327, "dur": 18, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308346, "dur": 18, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308365, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308367, "dur": 19, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308389, "dur": 19, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308410, "dur": 17, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308430, "dur": 22, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308453, "dur": 14, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308470, "dur": 24, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308497, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308525, "dur": 18, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308545, "dur": 20, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308568, "dur": 18, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308588, "dur": 20, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308610, "dur": 20, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308635, "dur": 31, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308668, "dur": 24, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308695, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308697, "dur": 31, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308730, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308754, "dur": 17, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308773, "dur": 18, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308793, "dur": 16, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308812, "dur": 19, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308833, "dur": 17, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308851, "dur": 18, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308872, "dur": 19, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308894, "dur": 19, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308915, "dur": 18, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308936, "dur": 43, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745308981, "dur": 22, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309006, "dur": 19, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309027, "dur": 21, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309050, "dur": 19, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309072, "dur": 160, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309234, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309235, "dur": 24, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309262, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309284, "dur": 21, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309307, "dur": 19, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309328, "dur": 21, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309351, "dur": 18, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309372, "dur": 16, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309389, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309410, "dur": 19, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309431, "dur": 25, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309458, "dur": 30, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309490, "dur": 17, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309510, "dur": 19, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309530, "dur": 43, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309577, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309606, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309628, "dur": 17, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309647, "dur": 1, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309649, "dur": 17, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309668, "dur": 20, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309691, "dur": 18, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309711, "dur": 18, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309731, "dur": 18, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309751, "dur": 17, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309770, "dur": 14, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309787, "dur": 46, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309835, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309858, "dur": 17, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309877, "dur": 20, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309899, "dur": 20, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309921, "dur": 19, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309942, "dur": 49, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309994, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745309996, "dur": 58, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310057, "dur": 1, "ph": "X", "name": "ProcessMessages 974", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310059, "dur": 33, "ph": "X", "name": "ReadAsync 974", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310094, "dur": 21, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310117, "dur": 11, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310129, "dur": 16, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310147, "dur": 53, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310202, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310225, "dur": 20, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310248, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310305, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310330, "dur": 28, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310360, "dur": 19, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310381, "dur": 18, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310402, "dur": 19, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310423, "dur": 19, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310444, "dur": 18, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310464, "dur": 9, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310474, "dur": 242, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310718, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310720, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310743, "dur": 17, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310762, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310784, "dur": 18, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310805, "dur": 18, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310824, "dur": 19, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310845, "dur": 19, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310866, "dur": 17, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310885, "dur": 17, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310904, "dur": 30, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310937, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310976, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745310999, "dur": 16, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311017, "dur": 18, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311037, "dur": 21, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311060, "dur": 19, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311081, "dur": 19, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311102, "dur": 19, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311122, "dur": 14, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311139, "dur": 19, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311160, "dur": 17, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311179, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311198, "dur": 19, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311219, "dur": 21, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311242, "dur": 16, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311262, "dur": 20, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311284, "dur": 39, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311326, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311359, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311361, "dur": 30, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311394, "dur": 19, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311416, "dur": 20, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311438, "dur": 17, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311457, "dur": 18, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311478, "dur": 18, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311498, "dur": 17, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311517, "dur": 19, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311538, "dur": 17, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311557, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311579, "dur": 19, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311600, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311622, "dur": 21, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311645, "dur": 19, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311666, "dur": 19, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311687, "dur": 17, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311706, "dur": 30, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311738, "dur": 24, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311765, "dur": 26, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311793, "dur": 18, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311813, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311841, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311844, "dur": 17, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311863, "dur": 19, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311885, "dur": 22, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311909, "dur": 19, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311930, "dur": 21, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311953, "dur": 19, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311974, "dur": 13, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745311990, "dur": 21, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312013, "dur": 21, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312037, "dur": 19, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312059, "dur": 18, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312079, "dur": 26, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312107, "dur": 19, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312128, "dur": 18, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312148, "dur": 17, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312167, "dur": 27, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312196, "dur": 19, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312218, "dur": 23, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312243, "dur": 16, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312261, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312280, "dur": 20, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312303, "dur": 24, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312328, "dur": 17, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312348, "dur": 20, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312370, "dur": 20, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312391, "dur": 18, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312412, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312435, "dur": 30, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312468, "dur": 21, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312491, "dur": 17, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312510, "dur": 20, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312532, "dur": 23, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312558, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312582, "dur": 20, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312604, "dur": 20, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312627, "dur": 19, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312648, "dur": 22, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312672, "dur": 14, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312688, "dur": 21, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312711, "dur": 26, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312739, "dur": 19, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312761, "dur": 96, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312859, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312861, "dur": 30, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312891, "dur": 1, "ph": "X", "name": "ProcessMessages 1538", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312893, "dur": 14, "ph": "X", "name": "ReadAsync 1538", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312909, "dur": 19, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312931, "dur": 19, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312952, "dur": 23, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745312979, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313005, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313006, "dur": 19, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313028, "dur": 17, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313047, "dur": 19, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313068, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313070, "dur": 20, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313093, "dur": 16, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313111, "dur": 23, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313137, "dur": 21, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313160, "dur": 19, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313181, "dur": 19, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313202, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313204, "dur": 18, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313224, "dur": 32, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313259, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313287, "dur": 20, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313309, "dur": 19, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313330, "dur": 24, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313357, "dur": 18, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313377, "dur": 17, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313395, "dur": 19, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313418, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313421, "dur": 18, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313441, "dur": 19, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313463, "dur": 23, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313488, "dur": 20, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313510, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313529, "dur": 17, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313549, "dur": 18, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313570, "dur": 21, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313593, "dur": 19, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313613, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313634, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313656, "dur": 18, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313676, "dur": 20, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313698, "dur": 19, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313719, "dur": 18, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313739, "dur": 18, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313760, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313780, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313801, "dur": 20, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313824, "dur": 18, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313844, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313867, "dur": 16, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313886, "dur": 18, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313907, "dur": 18, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313928, "dur": 19, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313949, "dur": 35, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745313986, "dur": 20, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314010, "dur": 18, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314030, "dur": 40, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314097, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314099, "dur": 38, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314139, "dur": 1, "ph": "X", "name": "ProcessMessages 1771", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314140, "dur": 20, "ph": "X", "name": "ReadAsync 1771", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314162, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314185, "dur": 17, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314204, "dur": 22, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314229, "dur": 26, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314257, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314259, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314284, "dur": 26, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314313, "dur": 20, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314336, "dur": 19, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314377, "dur": 20, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314398, "dur": 156, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314556, "dur": 45, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314602, "dur": 3, "ph": "X", "name": "ProcessMessages 3747", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314606, "dur": 25, "ph": "X", "name": "ReadAsync 3747", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314634, "dur": 17, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314653, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314672, "dur": 59, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314734, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314737, "dur": 42, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314781, "dur": 1, "ph": "X", "name": "ProcessMessages 1494", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314783, "dur": 22, "ph": "X", "name": "ReadAsync 1494", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314807, "dur": 20, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314829, "dur": 21, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314853, "dur": 18, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314873, "dur": 18, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314893, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314914, "dur": 18, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314935, "dur": 43, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314981, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745314982, "dur": 55, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315040, "dur": 1, "ph": "X", "name": "ProcessMessages 1006", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315042, "dur": 53, "ph": "X", "name": "ReadAsync 1006", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315098, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315101, "dur": 55, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315160, "dur": 31, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315192, "dur": 1, "ph": "X", "name": "ProcessMessages 1107", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315194, "dur": 33, "ph": "X", "name": "ReadAsync 1107", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315229, "dur": 30, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315261, "dur": 25, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315288, "dur": 30, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315320, "dur": 34, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315356, "dur": 43, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315402, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315404, "dur": 31, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315438, "dur": 32, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315472, "dur": 21, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315496, "dur": 21, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315519, "dur": 17, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315538, "dur": 18, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315558, "dur": 18, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315579, "dur": 21, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315602, "dur": 37, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315642, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315645, "dur": 56, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315709, "dur": 3, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315716, "dur": 61, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315781, "dur": 1, "ph": "X", "name": "ProcessMessages 962", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315784, "dur": 38, "ph": "X", "name": "ReadAsync 962", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315825, "dur": 43, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315871, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315873, "dur": 39, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315914, "dur": 31, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315946, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315948, "dur": 21, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315971, "dur": 22, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745315995, "dur": 26, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316024, "dur": 18, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316045, "dur": 21, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316068, "dur": 22, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316093, "dur": 27, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316122, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316146, "dur": 28, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316177, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316204, "dur": 20, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316226, "dur": 19, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316247, "dur": 20, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316270, "dur": 32, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316304, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316306, "dur": 39, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316348, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316350, "dur": 25, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316376, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316378, "dur": 27, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316407, "dur": 24, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316433, "dur": 2, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316436, "dur": 30, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316468, "dur": 27, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316498, "dur": 18, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316518, "dur": 20, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316540, "dur": 19, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316561, "dur": 18, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316581, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316602, "dur": 46, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316652, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316677, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316700, "dur": 18, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316720, "dur": 71, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316793, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316819, "dur": 24, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316845, "dur": 18, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316866, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316934, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316996, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745316998, "dur": 23, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317024, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317026, "dur": 64, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317095, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317143, "dur": 2, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317145, "dur": 18, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317167, "dur": 79, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317250, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317286, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317288, "dur": 38, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317331, "dur": 3, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317337, "dur": 38, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317378, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317405, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317407, "dur": 26, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317435, "dur": 57, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317494, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317516, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317518, "dur": 27, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317546, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317548, "dur": 20, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317570, "dur": 28, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317601, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317603, "dur": 44, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317649, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317668, "dur": 24, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317694, "dur": 20, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317717, "dur": 90, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317812, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317815, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317865, "dur": 3, "ph": "X", "name": "ProcessMessages 1065", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317870, "dur": 42, "ph": "X", "name": "ReadAsync 1065", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317915, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317952, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317955, "dur": 41, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745317999, "dur": 2, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318002, "dur": 37, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318041, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318076, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318078, "dur": 25, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318105, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318106, "dur": 23, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318133, "dur": 67, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318201, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318230, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318232, "dur": 27, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318261, "dur": 19, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318282, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318351, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318372, "dur": 27, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318403, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318405, "dur": 23, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318430, "dur": 16, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318449, "dur": 18, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318469, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318471, "dur": 132, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318606, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318627, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318628, "dur": 18, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318647, "dur": 1, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318649, "dur": 18, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318670, "dur": 19, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318691, "dur": 18, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318712, "dur": 123, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318839, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318859, "dur": 3, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318863, "dur": 17, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318882, "dur": 25, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318909, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318930, "dur": 21, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318953, "dur": 23, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745318978, "dur": 115, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319097, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319131, "dur": 41, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319174, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319175, "dur": 29, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319206, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319208, "dur": 16, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319226, "dur": 117, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319346, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319370, "dur": 21, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319395, "dur": 22, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319420, "dur": 29, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319451, "dur": 2, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319454, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319477, "dur": 74, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319553, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319579, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319602, "dur": 19, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319623, "dur": 53, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319679, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319702, "dur": 21, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319725, "dur": 20, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319747, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319749, "dur": 23, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319775, "dur": 18, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319797, "dur": 73, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319874, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319900, "dur": 26, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319930, "dur": 22, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319955, "dur": 39, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319997, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745319999, "dur": 24, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320024, "dur": 1, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320026, "dur": 22, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320050, "dur": 16, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320068, "dur": 22, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320092, "dur": 59, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320155, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320181, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320183, "dur": 20, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320206, "dur": 18, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320229, "dur": 65, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320296, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320317, "dur": 23, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320342, "dur": 21, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320367, "dur": 23, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320395, "dur": 19, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320416, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320439, "dur": 18, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320460, "dur": 18, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320481, "dur": 18, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320503, "dur": 61, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320566, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320569, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320615, "dur": 30, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320648, "dur": 16, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320668, "dur": 48, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320721, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320747, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320748, "dur": 41, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320793, "dur": 39, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320835, "dur": 2, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320838, "dur": 33, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320873, "dur": 2, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320876, "dur": 28, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320906, "dur": 2, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320909, "dur": 24, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320938, "dur": 53, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745320993, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321028, "dur": 34, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321064, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321066, "dur": 90, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321158, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321193, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321195, "dur": 28, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321229, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321233, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321271, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321305, "dur": 2, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321308, "dur": 36, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321346, "dur": 1, "ph": "X", "name": "ProcessMessages 1005", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321350, "dur": 22, "ph": "X", "name": "ReadAsync 1005", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321374, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321375, "dur": 29, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321407, "dur": 26, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321434, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321437, "dur": 27, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321468, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321469, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321523, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321555, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321557, "dur": 39, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321598, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321601, "dur": 51, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321655, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321687, "dur": 24, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321714, "dur": 58, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321775, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321831, "dur": 1, "ph": "X", "name": "ProcessMessages 1087", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321833, "dur": 19, "ph": "X", "name": "ReadAsync 1087", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321857, "dur": 54, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321915, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321937, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321939, "dur": 19, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321960, "dur": 20, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745321983, "dur": 54, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322039, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322041, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322059, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322061, "dur": 19, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322084, "dur": 17, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322102, "dur": 2, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322106, "dur": 18, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322127, "dur": 18, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322146, "dur": 54, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322204, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322225, "dur": 20, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322248, "dur": 19, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322268, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322271, "dur": 56, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322328, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322330, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322354, "dur": 18, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322375, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322377, "dur": 19, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322398, "dur": 59, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322459, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322491, "dur": 12, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322507, "dur": 22, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322534, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322567, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322587, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322589, "dur": 65, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322657, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322681, "dur": 19, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322705, "dur": 16, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322724, "dur": 60, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322788, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322813, "dur": 19, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322835, "dur": 19, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322856, "dur": 83, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322943, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745322973, "dur": 27, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323004, "dur": 73, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323080, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323113, "dur": 26, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323142, "dur": 20, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323166, "dur": 60, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323239, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323242, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323293, "dur": 2, "ph": "X", "name": "ProcessMessages 1252", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323297, "dur": 48, "ph": "X", "name": "ReadAsync 1252", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323349, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323394, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323397, "dur": 36, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323434, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323437, "dur": 64, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323505, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323551, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323554, "dur": 35, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323590, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323594, "dur": 49, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323644, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323646, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323685, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323688, "dur": 36, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323726, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323731, "dur": 51, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323785, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323827, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323831, "dur": 40, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323873, "dur": 3, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323878, "dur": 53, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323936, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323974, "dur": 1, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745323978, "dur": 27, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324006, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324008, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324075, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324079, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324147, "dur": 3, "ph": "X", "name": "ProcessMessages 1005", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324152, "dur": 25, "ph": "X", "name": "ReadAsync 1005", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324179, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324181, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324230, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324272, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324274, "dur": 39, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324315, "dur": 2, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324318, "dur": 48, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324369, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324401, "dur": 34, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324441, "dur": 2, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324445, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324470, "dur": 48, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324522, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324565, "dur": 2, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324568, "dur": 26, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324596, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324600, "dur": 56, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324659, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324686, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324688, "dur": 31, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324722, "dur": 22, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324746, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324749, "dur": 58, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324812, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324843, "dur": 25, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324870, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324872, "dur": 24, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324897, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324899, "dur": 67, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745324970, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325017, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325019, "dur": 39, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325060, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325062, "dur": 53, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325118, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325120, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325156, "dur": 24, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325183, "dur": 62, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325248, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325286, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325288, "dur": 29, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325318, "dur": 2, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325321, "dur": 29, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325353, "dur": 24, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325381, "dur": 23, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325407, "dur": 19, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325427, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325429, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325499, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325526, "dur": 17, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325544, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325546, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325568, "dur": 19, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325590, "dur": 17, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325611, "dur": 20, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325633, "dur": 17, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325652, "dur": 17, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325671, "dur": 23, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325697, "dur": 52, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325751, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325782, "dur": 20, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325805, "dur": 19, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325826, "dur": 21, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325850, "dur": 21, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325874, "dur": 18, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325895, "dur": 14, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325911, "dur": 20, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325933, "dur": 1, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325934, "dur": 17, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745325954, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745326011, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745326042, "dur": 84, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745326132, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745326196, "dur": 608, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745326808, "dur": 82, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745326893, "dur": 16, "ph": "X", "name": "ProcessMessages 2340", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745326910, "dur": 31, "ph": "X", "name": "ReadAsync 2340", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745326942, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745326945, "dur": 36, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745326984, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745326986, "dur": 34, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745327024, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745327026, "dur": 38, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745327067, "dur": 3, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745327071, "dur": 33, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745327108, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745327112, "dur": 42, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745327158, "dur": 3, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745327163, "dur": 59, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745327225, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745327228, "dur": 35, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745327266, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745327273, "dur": 40, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745327316, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745327319, "dur": 45, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745327367, "dur": 3, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745327371, "dur": 692, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745328065, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745328071, "dur": 77, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745328151, "dur": 18, "ph": "X", "name": "ProcessMessages 2660", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745328171, "dur": 5189, "ph": "X", "name": "ReadAsync 2660", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745333371, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745333376, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745333428, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745333430, "dur": 5039, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745338478, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745338482, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745338536, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745338540, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745338591, "dur": 186, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745338784, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745338820, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745338822, "dur": 3976, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745342806, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745342811, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745342838, "dur": 113, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745342956, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745342986, "dur": 350, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745343341, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745343383, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745343385, "dur": 256, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745343647, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745343685, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745343688, "dur": 35, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745343725, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745343727, "dur": 25, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745343755, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745343835, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745343861, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745343897, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745343928, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745343980, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344004, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344094, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344119, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344361, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344392, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344394, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344435, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344459, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344461, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344486, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344488, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344607, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344630, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344633, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344656, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344658, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344683, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344705, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344745, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344766, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344825, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344855, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344891, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344932, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344958, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344983, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745344984, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345010, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345039, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345041, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345067, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345140, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345167, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345198, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345223, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345324, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345348, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345350, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345387, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345411, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345438, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345464, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345490, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345492, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345519, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345579, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345613, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345615, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345643, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345666, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345697, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345730, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345773, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345808, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345811, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345856, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345894, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345895, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745345925, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346011, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346054, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346093, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346122, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346153, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346177, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346239, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346266, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346268, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346317, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346352, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346355, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346386, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346388, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346435, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346461, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346515, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346544, "dur": 201, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346749, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346777, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346800, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346827, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346896, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346932, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346934, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346962, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745346989, "dur": 240, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745347233, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745347257, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745347261, "dur": 151, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745347417, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745347453, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745347455, "dur": 26, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745347486, "dur": 153, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745347645, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745347685, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745347765, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745347794, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745347796, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745347820, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745347853, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745347875, "dur": 691, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745348570, "dur": 48, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745348620, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745348623, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745348664, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745348700, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745348702, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745348728, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745348730, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745348764, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745348781, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745348824, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745348846, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745348867, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745348887, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745349010, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745349043, "dur": 143, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745349193, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745349230, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745349232, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745349269, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745349314, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745349316, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745349353, "dur": 484, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745349841, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745349875, "dur": 105, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745349984, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745350027, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745350030, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745350068, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745350070, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745350153, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745350192, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745350275, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745350305, "dur": 201, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745350508, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745350540, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745350542, "dur": 135, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745350682, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745350708, "dur": 286, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745351000, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745351017, "dur": 173, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745351195, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745351226, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745351228, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745351291, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745351316, "dur": 495, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745351815, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745351848, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745351850, "dur": 148, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745352002, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745352043, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745352088, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745352120, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745352226, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745352250, "dur": 575, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745352829, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745352902, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745352906, "dur": 79162, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745432076, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745432079, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745432138, "dur": 1461, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745433603, "dur": 2413, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436021, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436023, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436062, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436064, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436099, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436133, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436193, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436233, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436235, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436273, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436275, "dur": 158, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436438, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436466, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436468, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436623, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436627, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436666, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745436669, "dur": 757, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745437430, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745437478, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745437480, "dur": 657, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745438142, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745438174, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745438176, "dur": 151, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745438331, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745438346, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745438467, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745438469, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745438508, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745438510, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745438537, "dur": 305, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745438847, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745438874, "dur": 156, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745439034, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745439060, "dur": 560, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745439625, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745439656, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745439658, "dur": 536, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745440199, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745440237, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745440239, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745440269, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745440270, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745440339, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745440366, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745440368, "dur": 207, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745440582, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745440607, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745440644, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745440665, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745440831, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745440855, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745440879, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745441003, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745441028, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745441030, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745441163, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745441196, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745441226, "dur": 219, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745441449, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745441481, "dur": 135, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745441619, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745441649, "dur": 355, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442008, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442038, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442041, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442283, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442308, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442362, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442394, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442395, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442446, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442477, "dur": 267, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442748, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442778, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442781, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442803, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442842, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442874, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442876, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442904, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745442906, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443069, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443098, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443100, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443129, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443132, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443159, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443161, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443194, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443196, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443227, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443228, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443253, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443255, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443279, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443280, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443305, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443331, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443379, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443406, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443434, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443436, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443473, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443476, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443509, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443511, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443539, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443567, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443596, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443621, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443623, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443649, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443677, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443702, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443729, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443767, "dur": 28, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443798, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443844, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443847, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443874, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443876, "dur": 28, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443906, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443909, "dur": 26, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443938, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745443940, "dur": 86, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444029, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444032, "dur": 33, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444068, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444072, "dur": 114, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444191, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444193, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444220, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444252, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444254, "dur": 24, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444281, "dur": 152, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444438, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444477, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444479, "dur": 43, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444526, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444557, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444582, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444607, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444608, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444637, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444669, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444671, "dur": 148, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444823, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444859, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444861, "dur": 72, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444936, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444963, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153745444987, "dur": 19779521, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765224513, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765224516, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765224604, "dur": 18, "ph": "X", "name": "ProcessMessages 8958", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765224623, "dur": 16072, "ph": "X", "name": "ReadAsync 8958", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765240702, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765240705, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765240757, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765240760, "dur": 168264, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765409036, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765409041, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765409062, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765409065, "dur": 558464, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765967534, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765967537, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765967610, "dur": 31, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765967643, "dur": 18, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765967662, "dur": 5178, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765972844, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765972845, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765972878, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765972880, "dur": 1433, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765974316, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765974318, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765974362, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153765974389, "dur": 150276, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153766124675, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153766124680, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153766124728, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153766124734, "dur": 740, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153766125478, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153766125511, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153766125537, "dur": 2339714, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768465259, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768465264, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768465299, "dur": 26, "ph": "X", "name": "ProcessMessages 8563", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768465326, "dur": 240458, "ph": "X", "name": "ReadAsync 8563", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768705793, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768705798, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768705843, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768705846, "dur": 75539, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768781393, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768781398, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768781455, "dur": 20, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768781476, "dur": 5290, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768786771, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768786774, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768786833, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768786836, "dur": 377, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768787219, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768787223, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768787248, "dur": 27, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153768787277, "dur": 329266, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153769116553, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153769116563, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153769116640, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153769116645, "dur": 565, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153769117216, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153769117220, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153769117246, "dur": 39, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153769117287, "dur": 871, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153769118164, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153769118167, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153769118189, "dur": 479, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 21928, "tid": 12884901888, "ts": 1750153769118673, "dur": 6484, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 21928, "tid": 803115, "ts": 1750153769139081, "dur": 1356, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 21928, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 21928, "tid": 8589934592, "ts": 1750153745255608, "dur": 135644, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 21928, "tid": 8589934592, "ts": 1750153745391255, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 21928, "tid": 8589934592, "ts": 1750153745391262, "dur": 1011, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 21928, "tid": 803115, "ts": 1750153769140439, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 21928, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 21928, "tid": 4294967296, "ts": 1750153745197334, "dur": 23929648, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 21928, "tid": 4294967296, "ts": 1750153745200668, "dur": 49502, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 21928, "tid": 4294967296, "ts": 1750153769127215, "dur": 4368, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 21928, "tid": 4294967296, "ts": 1750153769129500, "dur": 115, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 21928, "tid": 4294967296, "ts": 1750153769131727, "dur": 38, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 21928, "tid": 803115, "ts": 1750153769140446, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750153745267188, "dur": 37017, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750153745304216, "dur": 807, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750153745305141, "dur": 64, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1750153745305206, "dur": 400, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750153745306901, "dur": 241, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_99EDDAA1E2125F9F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750153745308049, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_BFEF6BF3A445E475.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750153745310028, "dur": 149, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750153745311023, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750153745305623, "dur": 21342, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750153745326979, "dur": 23791230, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750153769118211, "dur": 444, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750153769118656, "dur": 51, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750153769118944, "dur": 103, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750153769119088, "dur": 1142, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750153745306214, "dur": 20773, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745327157, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_6E196A46756374CC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750153745327369, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_E6B906AFC5CA6A31.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750153745327790, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750153745327895, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750153745328038, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750153745328132, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750153745328400, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750153745328487, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750153745328714, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750153745328865, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745329219, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745329449, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745329656, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745329859, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745330087, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745330260, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745330801, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745332849, "dur": 527, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f10877fbf761\\Editor\\Data\\Legacy\\StickyNoteData0.cs"}}, {"pid": 12345, "tid": 1, "ts": 1750153745331719, "dur": 1727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745333446, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745334500, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745334681, "dur": 1339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745336020, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745337113, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745338260, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745339418, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745340761, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745341516, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745341711, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745341950, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745342185, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745342391, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745343462, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745343757, "dur": 881, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745344639, "dur": 730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750153745345389, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750153745345907, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745346569, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745346754, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745347309, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745347886, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745348217, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745348406, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750153745348577, "dur": 1314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750153745349967, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750153745350118, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750153745350802, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750153745350954, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750153745351470, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750153745351608, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750153745351956, "dur": 871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745352827, "dur": 82288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745435117, "dur": 1877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750153745436995, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745437053, "dur": 2076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750153745439130, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745439442, "dur": 2154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750153745441597, "dur": 1609, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750153745443219, "dur": 2157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750153745445444, "dur": 23672759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745306326, "dur": 20711, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745327150, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4B4D0A166E0D20DC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750153745327374, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745328078, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750153745328571, "dur": 300, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750153745328872, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745329307, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745329500, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745329722, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745329919, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745330105, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745330298, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745331482, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745332893, "dur": 513, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f10877fbf761\\Editor\\Data\\Graphs\\GraphSetup.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750153745332278, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745333615, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745334582, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745335898, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745336924, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745338368, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745339215, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745340119, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745341443, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745341621, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745341833, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745342017, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745342237, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745343076, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745343745, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745344343, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750153745344591, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750153745345295, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745345397, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750153745345564, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750153745346655, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745346949, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745347301, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745347860, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745348224, "dur": 4580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745352805, "dur": 40398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745394820, "dur": 163, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 2, "ts": 1750153745394983, "dur": 635, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 2, "ts": 1750153745393205, "dur": 2450, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745395655, "dur": 39385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745435044, "dur": 3244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750153745438290, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745438346, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750153745438412, "dur": 2645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750153745441059, "dur": 1323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745442391, "dur": 2514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750153745444949, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745445151, "dur": 735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750153745445907, "dur": 23672315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745306388, "dur": 20697, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745327266, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_88EB000108787B7B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750153745327394, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_8D701D583C70E786.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750153745327774, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750153745327997, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750153745328111, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1750153745328179, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750153745328303, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750153745328490, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750153745328859, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745329237, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745329469, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745329688, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745329905, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745330106, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745330288, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745331343, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745332177, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745332807, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745333018, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745333177, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745333348, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745333536, "dur": 1452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745334988, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745336317, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745337685, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745338984, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745339198, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745339366, "dur": 1496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745341822, "dur": 606, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework.performance@92d1d09a72ed\\Runtime\\Metadata.cs"}}, {"pid": 12345, "tid": 3, "ts": 1750153745340862, "dur": 1567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745342429, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745343311, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745343755, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745344368, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750153745344573, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745344779, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750153745345475, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745345627, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745345690, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745345802, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750153745346079, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750153745346508, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745347063, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745347305, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745347864, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745348222, "dur": 2585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745350809, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750153745350920, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750153745351230, "dur": 1581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745352812, "dur": 82214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745435027, "dur": 2083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750153745437165, "dur": 3951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750153745441117, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750153745441278, "dur": 2061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750153745443389, "dur": 2098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750153745445531, "dur": 23672685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745306498, "dur": 20621, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745327124, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_9F0F250247658205.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750153745327424, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_9CC28255281D0D3B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750153745327805, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750153745327971, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1750153745328215, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750153745328327, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750153745328402, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1750153745328842, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745329262, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745329471, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745329681, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745329861, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745330106, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745330432, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745331294, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745331957, "dur": 787, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f10877fbf761\\Editor\\Data\\Interfaces\\IMayRequireTangent.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750153745332841, "dur": 523, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f10877fbf761\\Editor\\Data\\Interfaces\\IMayRequirePositionPredisplacement.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750153745331957, "dur": 2713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745334670, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745335992, "dur": 1482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745337475, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745338407, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745339286, "dur": 1385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745340672, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745340870, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745341869, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745342290, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745343520, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745343750, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745344345, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750153745344601, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750153745345194, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745345324, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750153745345741, "dur": 622, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745346395, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745346563, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750153745346826, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750153745347375, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745347682, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745347901, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745348224, "dur": 4608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745352832, "dur": 82196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745435029, "dur": 2296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750153745437377, "dur": 1979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750153745439357, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745439417, "dur": 2045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750153745441463, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745441937, "dur": 2718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750153745444656, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745444721, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750153745444841, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750153745445043, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750153745445585, "dur": 23672641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745306246, "dur": 20755, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745327140, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_025D68ED9798F53F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750153745327191, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745327380, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745327967, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1750153745328110, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750153745328181, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750153745328288, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750153745328488, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750153745328830, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745329190, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745329376, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745329643, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745329832, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745330028, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745330234, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745330800, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745332041, "dur": 678, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f10877fbf761\\Editor\\Data\\Nodes\\Math\\Vector\\SphereMaskNode.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750153745331435, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745332720, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745332903, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745333074, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745333234, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745333698, "dur": 750, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.State\\State.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750153745333411, "dur": 2582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745335993, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745337359, "dur": 1395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745338755, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745338991, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745339218, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745340554, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745340801, "dur": 989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745341791, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745342035, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745342247, "dur": 97, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745342344, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745343482, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745343766, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745344363, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750153745344580, "dur": 1273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750153745345854, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745345937, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750153745346110, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750153745346279, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750153745346819, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745347312, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745347887, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745348228, "dur": 4578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745352807, "dur": 42855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745395663, "dur": 41362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745437026, "dur": 2688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750153745439715, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745439785, "dur": 3260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750153745443046, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750153745443306, "dur": 2416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750153745445772, "dur": 23672458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745306283, "dur": 20728, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745327015, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_1BD15D364A91220E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750153745327241, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_4B227E8F42648F37.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750153745327401, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_E3FF4E8988E07430.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750153745327793, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750153745328000, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1750153745328851, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745329259, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745329495, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745329700, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745330094, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745331017, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745331655, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745332301, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745333488, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745334409, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745335695, "dur": 1566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745337261, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745338736, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745338913, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745339133, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745339321, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745340717, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745340986, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745341150, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745341352, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745341532, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745341758, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745342016, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745342232, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745342942, "dur": 801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745343770, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745344349, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750153745344595, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745344651, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750153745345200, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745345421, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745345889, "dur": 680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745346569, "dur": 737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745347306, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745347862, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745348220, "dur": 1417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745349639, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750153745349823, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750153745350162, "dur": 2668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745352830, "dur": 82225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745435056, "dur": 2032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750153745437117, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750153745437174, "dur": 3157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750153745440332, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750153745440556, "dur": 1966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750153745442565, "dur": 2769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750153745445389, "dur": 23672821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745306316, "dur": 20708, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745327031, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_D44F1D7947A8470C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750153745327409, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C9BF491CE9429A6C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750153745327789, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750153745327873, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1750153745328130, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1750153745328393, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750153745328492, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750153745328560, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750153745328836, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745329139, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745329366, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745329581, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745329808, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745330018, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745330207, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745330373, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745331283, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745331977, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745332756, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745332958, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745333145, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745333294, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745333472, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745334213, "dur": 1452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745335665, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745336681, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745337587, "dur": 1384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745338972, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745339207, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745339977, "dur": 1409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745341387, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745341571, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745341830, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745342083, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745342337, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745343460, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745343759, "dur": 834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745344594, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750153745344780, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745344837, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750153745345329, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750153745345545, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750153745345817, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750153745346346, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750153745346513, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750153745347021, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745347298, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750153745347454, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750153745347894, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745348229, "dur": 4586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745352816, "dur": 82207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745435042, "dur": 3989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750153745439033, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745439276, "dur": 2485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750153745441799, "dur": 2055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750153745443855, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745444269, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745444387, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745444674, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750153745444923, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153745445187, "dur": 20528406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750153765973626, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750153765973595, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750153765973786, "dur": 1481, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750153765975271, "dur": 3142968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745306351, "dur": 20700, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745327378, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_1E5BE8A4F6F01B45.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750153745327973, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1750153745328130, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750153745328333, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1750153745328561, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750153745328819, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745329129, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745329725, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745330155, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745330333, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745330919, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745331632, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745332521, "dur": 2024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745334545, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745334920, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745336147, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745337275, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745338493, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745339173, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745339354, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745340437, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745341659, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745341891, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745342127, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745342408, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745343347, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745343768, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745344353, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750153745344921, "dur": 961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750153745345917, "dur": 520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745346442, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750153745346643, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750153745347192, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750153745347289, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745347396, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750153745347667, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745347753, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745347881, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745348213, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750153745348347, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750153745348750, "dur": 4069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745352819, "dur": 82218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745435041, "dur": 1955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750153745436998, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745437129, "dur": 2106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750153745439236, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745439414, "dur": 1971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750153745441386, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745441590, "dur": 2344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750153745443936, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745444049, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745444191, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745444427, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745444603, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745445035, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750153745445454, "dur": 23672747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745306378, "dur": 20692, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745327310, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_739DB9D321F7D359.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750153745327374, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_7D559772BEA705DB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750153745327772, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750153745328067, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750153745328828, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745329140, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745329322, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745329501, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745329707, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745329935, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745330372, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745330905, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745331454, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745332782, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745332964, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745333102, "dur": 129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745333231, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745333369, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745333546, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745334763, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745335738, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745337040, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745338266, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745339293, "dur": 1482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745340776, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745341985, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745342228, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745342823, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745343749, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745344360, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750153745344571, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745344626, "dur": 1343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750153745346007, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745346109, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750153745346258, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750153745346855, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745347295, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750153745347457, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750153745348210, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750153745348366, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750153745348851, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750153745348969, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750153745349525, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153745349631, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750153745349770, "dur": 1114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750153745350937, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750153745351080, "dur": 1013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750153745352143, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750153745352234, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750153745352800, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750153745352916, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750153745353144, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750153745354125, "dur": 19872423, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750153765228260, "dur": 12943, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750153765227954, "dur": 13323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750153765241528, "dur": 70, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153765242133, "dur": 3224268, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750153768467720, "dur": 235859, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750153768467714, "dur": 237728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750153768706499, "dur": 163, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750153768706898, "dur": 75422, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750153768787538, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1750153768787530, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1750153768787683, "dur": 445, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1750153768788131, "dur": 330073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745306433, "dur": 20665, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745327447, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F75569858C7FEC6C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750153745327790, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750153745328125, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1750153745328296, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750153745328825, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745329165, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745329355, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745329567, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745329778, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745329968, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745330184, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745330420, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745331220, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745331806, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745332441, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745333736, "dur": 1567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745335303, "dur": 1419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745336723, "dur": 1621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745338344, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745339426, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745340817, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745341922, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745342283, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745343187, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745343754, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745344341, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750153745344533, "dur": 492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745345033, "dur": 1427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750153745346460, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745346560, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750153745346873, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745346980, "dur": 1259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750153745348240, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745348402, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750153745348574, "dur": 987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750153745349635, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750153745349804, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750153745350245, "dur": 2580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745352825, "dur": 82196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745435023, "dur": 2067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750153745437092, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745437179, "dur": 2248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750153745439461, "dur": 2017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750153745441515, "dur": 3113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750153745444676, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745444727, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750153745444880, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1750153745445024, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153745445201, "dur": 23342336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750153768787570, "dur": 329837, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750153768787539, "dur": 329871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750153769117441, "dur": 706, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750153745306489, "dur": 20619, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745327114, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_7B06E7D06A357F34.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750153745327395, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3EE9BB20E930FDFD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750153745327794, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1750153745328073, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750153745328385, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750153745328708, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750153745328885, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745329292, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745329517, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745329736, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745329933, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745330175, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745330982, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745331607, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745332905, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f10877fbf761\\Editor\\Data\\Graphs\\BitangentMaterialSlot.cs"}}, {"pid": 12345, "tid": 11, "ts": 1750153745332285, "dur": 1283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745333568, "dur": 1724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745335292, "dur": 2005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745337298, "dur": 1722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745339020, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745339359, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745340426, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745341706, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745341964, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745342190, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745342452, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745342828, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745343747, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745344367, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750153745344588, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745344659, "dur": 701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750153745345360, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745345629, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745345909, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745346568, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750153745346767, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745346828, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750153745347219, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745347304, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745347861, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745348220, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750153745348344, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750153745348647, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745348726, "dur": 4074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745352805, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750153745352961, "dur": 83195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745436157, "dur": 2369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750153745438528, "dur": 545, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745439099, "dur": 2641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750153745441776, "dur": 1875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750153745443835, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745444130, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745444573, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745444840, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1750153745445036, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750153745445539, "dur": 23672680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745306573, "dur": 20560, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745327137, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_100C028F850EE8CE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750153745327298, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_40EACCB6DE9B854E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750153745327388, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_136791916CB4C4E9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750153745327805, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750153745327965, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750153745328105, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750153745328428, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1750153745328848, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745329243, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745329440, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745329662, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745329849, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745330044, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745330209, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745330389, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745331041, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745331648, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745333614, "dur": 719, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.State\\Flow\\UnitBaseStateExtensions.cs"}}, {"pid": 12345, "tid": 12, "ts": 1750153745332580, "dur": 2443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745335023, "dur": 1564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745336587, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745337736, "dur": 1409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745339145, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745339334, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745340497, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745341553, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745341885, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745342075, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745342280, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745343440, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745343753, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745344363, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750153745344580, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745344657, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750153745345173, "dur": 937, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745346163, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750153745346405, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750153745347094, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745347308, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745347863, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745348218, "dur": 636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745348856, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750153745349016, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750153745349705, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750153745349821, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750153745350263, "dur": 2563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745352827, "dur": 82207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745435035, "dur": 2072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750153745437135, "dur": 2524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750153745439660, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745439781, "dur": 2275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750153745442103, "dur": 2517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750153745444670, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750153745444749, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745444879, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745445045, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1750153745445121, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750153745445779, "dur": 23672445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750153745306622, "dur": 20520, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750153745327146, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_1DC9F96436961A23.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750153745327361, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750153745327831, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750153745327959, "dur": 11364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750153745339395, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750153745339477, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750153745339714, "dur": 3957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750153745343787, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750153745343895, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750153745344339, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750153745344582, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750153745345221, "dur": 469, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750153745345703, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750153745345764, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750153745345958, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750153745346374, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750153745346717, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750153745346814, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750153745347302, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750153745347859, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750153745348214, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750153745348369, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750153745348745, "dur": 4077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750153745352823, "dur": 82228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750153745435052, "dur": 1936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750153745436990, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750153745437159, "dur": 2980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750153745440140, "dur": 980, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750153745441130, "dur": 2028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750153745443160, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750153745443690, "dur": 2158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750153745445892, "dur": 23672329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745306654, "dur": 20497, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745327155, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C5179C2B98A35602.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750153745327387, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_1DB8604BF3C2B15B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750153745327792, "dur": 225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1750153745328077, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750153745328339, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1750153745328499, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750153745328846, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745329211, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745329386, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745329625, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745329847, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745330065, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745330266, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745330443, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745331387, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745332104, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745333392, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745334115, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745335273, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745336363, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745338010, "dur": 1339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745339349, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745340550, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745341153, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745341331, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745341537, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745341759, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745341935, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745342131, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745342367, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745343437, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745343762, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745344361, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750153745344582, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745344647, "dur": 913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750153745345608, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745345808, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745346115, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750153745346531, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745346606, "dur": 693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745347300, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750153745347462, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750153745347917, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745348225, "dur": 4577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745352804, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750153745353028, "dur": 82020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745435049, "dur": 1841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750153745436970, "dur": 4914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750153745441885, "dur": 1055, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750153745442970, "dur": 2558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750153745445583, "dur": 23672629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745306719, "dur": 20446, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745327442, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_CF814559B1575A99.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750153745327775, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1750153745327833, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750153745328009, "dur": 6189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750153745334315, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745335350, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745336704, "dur": 1405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745338109, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745339072, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745339302, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745340398, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745341368, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745341582, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745341840, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745342137, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745342414, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745343424, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745343763, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745344381, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750153745344620, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750153745344794, "dur": 1104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745345905, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750153745346297, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745346444, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745346567, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750153745346776, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750153745347175, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745347304, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745347877, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745348219, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750153745348380, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750153745348857, "dur": 3964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745352821, "dur": 82224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745435056, "dur": 2016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750153745437073, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745437546, "dur": 2014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750153745439561, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153745439971, "dur": 2133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750153745442148, "dur": 2964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750153745445180, "dur": 19782777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153765227977, "dur": 178729, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750153765227959, "dur": 180209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750153765409621, "dur": 255, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750153765410183, "dur": 558274, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750153765973592, "dur": 151926, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750153765973585, "dur": 151936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750153766125565, "dur": 853, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750153766126424, "dur": 2991818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745306753, "dur": 20426, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745327433, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B739A7DF6FD203B7.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750153745327793, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1750153745327918, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750153745327991, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750153745328118, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1750153745328258, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750153745328443, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750153745328822, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745329178, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745329420, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745329660, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745329869, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745330139, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745330351, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745330855, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745331696, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745332629, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745333701, "dur": 1688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745335389, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745336770, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745338057, "dur": 1854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745339912, "dur": 1748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745341661, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745341905, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745342131, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745342355, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745343396, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745343756, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745344356, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750153745344595, "dur": 1197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750153745345793, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745345953, "dur": 611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745346565, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750153745346816, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750153745347304, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750153745347458, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750153745347857, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 16, "ts": 1750153745348385, "dur": 150, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745348803, "dur": 84204, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 16, "ts": 1750153745435030, "dur": 1946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750153745437013, "dur": 2032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750153745439082, "dur": 1934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750153745441017, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745441188, "dur": 1954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750153745443143, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745443714, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745444135, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745444352, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745444450, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745444689, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750153745444833, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745445028, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750153745445399, "dur": 23672807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750153769124602, "dur": 1141, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 21928, "tid": 803115, "ts": 1750153769140992, "dur": 2318, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 21928, "tid": 803115, "ts": 1750153769143362, "dur": 1841, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 21928, "tid": 803115, "ts": 1750153769136902, "dur": 9229, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}