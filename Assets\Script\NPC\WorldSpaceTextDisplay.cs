using UnityEngine;
using TMPro;

namespace NPCSystem
{
    /// <summary>
    /// Hiển thị text trên đầu NPC trong world space
    /// Dùng để hiển thị "I want to buy..." và các thông báo khác
    /// </summary>
    public class WorldSpaceTextDisplay : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎨 Text Display Settings")]
        [SerializeField, Toolt<PERSON>("TextMeshPro component để hiển thị text")]
        private TextMeshProUGUI m_TextComponent;
        
        [SerializeField, Tooltip("Canvas chứa text")]
        private Canvas m_Canvas;
        
        [SerializeField, Tooltip("Khoảng cách từ NPC đến text")]
        private Vector3 m_OffsetPosition = new Vector3(0, 2.5f, 0);
        
        [Header("🎯 Display Behavior")]
        [SerializeField, Tooltip("Thời gian hiển thị text (gi<PERSON><PERSON>, 0 = vô hạn)")]
        private float m_ThoiGianHienThi = 5f;
        
        [Serial<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Text có luôn nhìn về camera")]
        private bool m_LuonNhinCamera = true;
        
        [SerializeField, Tooltip("Kích thước text")]
        private float m_KichThuocText = 24f;
        
        [SerializeField, Tooltip("Màu text mặc định")]
        private Color m_MauTextMacDinh = Color.white;
        
        [Header("🎭 Animation")]
        [SerializeField, Tooltip("Có hiệu ứng fade in/out")]
        private bool m_CoHieuUngFade = true;
        
        [SerializeField, Tooltip("Thời gian fade")]
        private float m_ThoiGianFade = 0.5f;
        
        [SerializeField, Tooltip("Có hiệu ứng bounce")]
        private bool m_CoHieuUngBounce = true;
        #endregion

        #region Private Fields
        private Camera m_MainCamera;
        private Transform m_NPCTransform;
        private Coroutine m_HideCoroutine;
        private Vector3 m_ScaleGoc;
        private bool m_DangHienThi = false;
        #endregion

        #region Properties
        public bool DangHienThi => m_DangHienThi;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            KhoiTaoComponents();
        }

        private void Start()
        {
            m_MainCamera = Camera.main;
            m_NPCTransform = transform.parent;
            
            if (m_TextComponent != null)
            {
                m_ScaleGoc = m_TextComponent.transform.localScale;
            }
            
            AnText();
        }

        private void Update()
        {
            if (m_DangHienThi && m_LuonNhinCamera && m_MainCamera != null)
            {
                CapNhatHuongNhinCamera();
            }
            
            CapNhatViTri();
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Hiển thị text với nội dung cụ thể
        /// </summary>
        public void HienThiText(string noiDung, Color? mauText = null, float? thoiGianHienThi = null)
        {
            if (m_TextComponent == null) return;

            // Dừng coroutine ẩn trước đó
            if (m_HideCoroutine != null)
            {
                StopCoroutine(m_HideCoroutine);
                m_HideCoroutine = null;
            }

            // Thiết lập text
            m_TextComponent.text = noiDung;
            m_TextComponent.color = mauText ?? m_MauTextMacDinh;
            m_TextComponent.fontSize = m_KichThuocText;

            // Hiển thị canvas
            if (m_Canvas != null)
            {
                m_Canvas.gameObject.SetActive(true);
            }

            m_DangHienThi = true;

            // Hiệu ứng hiển thị
            if (m_CoHieuUngFade)
            {
                StartCoroutine(FadeInEffect());
            }
            
            if (m_CoHieuUngBounce)
            {
                StartCoroutine(BounceEffect());
            }

            // Tự động ẩn sau thời gian
            float thoiGian = thoiGianHienThi ?? m_ThoiGianHienThi;
            if (thoiGian > 0)
            {
                m_HideCoroutine = StartCoroutine(AutoHideAfterTime(thoiGian));
            }

            Debug.Log($"[WorldSpaceText-{gameObject.name}] Hiển thị: {noiDung}");
        }

        /// <summary>
        /// Hiển thị text "I want to buy..." cho món hàng cụ thể
        /// </summary>
        public void HienThiWantToBuy(string tenMonHang, int giaHang)
        {
            string noiDung = $"I want to buy {tenMonHang}\n({giaHang} Lea)";
            HienThiText(noiDung, Color.yellow);
        }

        /// <summary>
        /// Ẩn text
        /// </summary>
        public void AnText()
        {
            if (m_HideCoroutine != null)
            {
                StopCoroutine(m_HideCoroutine);
                m_HideCoroutine = null;
            }

            if (m_CoHieuUngFade && m_DangHienThi)
            {
                StartCoroutine(FadeOutEffect());
            }
            else
            {
                AnTextTrucTiep();
            }
        }

        /// <summary>
        /// Thay đổi màu text
        /// </summary>
        public void ThayDoiMau(Color mauMoi)
        {
            if (m_TextComponent != null)
            {
                m_TextComponent.color = mauMoi;
            }
        }

        /// <summary>
        /// Thay đổi kích thước text
        /// </summary>
        public void ThayDoiKichThuoc(float kichThuocMoi)
        {
            m_KichThuocText = kichThuocMoi;
            if (m_TextComponent != null)
            {
                m_TextComponent.fontSize = m_KichThuocText;
            }
        }
        #endregion

        #region Private Methods
        private void KhoiTaoComponents()
        {
            // Tự động tìm components nếu chưa được gán
            if (m_Canvas == null)
            {
                m_Canvas = GetComponentInChildren<Canvas>();
            }
            
            if (m_TextComponent == null)
            {
                m_TextComponent = GetComponentInChildren<TextMeshProUGUI>();
            }

            // Thiết lập canvas
            if (m_Canvas != null)
            {
                m_Canvas.renderMode = RenderMode.WorldSpace;
                m_Canvas.worldCamera = Camera.main;
            }
        }

        private void CapNhatViTri()
        {
            if (m_NPCTransform != null)
            {
                transform.position = m_NPCTransform.position + m_OffsetPosition;
            }
        }

        private void CapNhatHuongNhinCamera()
        {
            if (m_Canvas != null && m_MainCamera != null)
            {
                Vector3 direction = m_MainCamera.transform.position - m_Canvas.transform.position;
                m_Canvas.transform.rotation = Quaternion.LookRotation(direction);
            }
        }

        private void AnTextTrucTiep()
        {
            if (m_Canvas != null)
            {
                m_Canvas.gameObject.SetActive(false);
            }
            m_DangHienThi = false;
        }

        private System.Collections.IEnumerator AutoHideAfterTime(float thoiGian)
        {
            yield return new WaitForSeconds(thoiGian);
            AnText();
        }

        private System.Collections.IEnumerator FadeInEffect()
        {
            if (m_TextComponent == null) yield break;

            float elapsed = 0f;
            Color colorGoc = m_TextComponent.color;
            Color colorTrongSuot = new Color(colorGoc.r, colorGoc.g, colorGoc.b, 0f);
            
            m_TextComponent.color = colorTrongSuot;

            while (elapsed < m_ThoiGianFade)
            {
                elapsed += Time.deltaTime;
                float alpha = Mathf.Lerp(0f, colorGoc.a, elapsed / m_ThoiGianFade);
                m_TextComponent.color = new Color(colorGoc.r, colorGoc.g, colorGoc.b, alpha);
                yield return null;
            }

            m_TextComponent.color = colorGoc;
        }

        private System.Collections.IEnumerator FadeOutEffect()
        {
            if (m_TextComponent == null) yield break;

            float elapsed = 0f;
            Color colorGoc = m_TextComponent.color;

            while (elapsed < m_ThoiGianFade)
            {
                elapsed += Time.deltaTime;
                float alpha = Mathf.Lerp(colorGoc.a, 0f, elapsed / m_ThoiGianFade);
                m_TextComponent.color = new Color(colorGoc.r, colorGoc.g, colorGoc.b, alpha);
                yield return null;
            }

            AnTextTrucTiep();
        }

        private System.Collections.IEnumerator BounceEffect()
        {
            if (m_TextComponent == null) yield break;

            Vector3 scaleGoc = m_ScaleGoc;
            Vector3 scaleLon = scaleGoc * 1.2f;
            
            float elapsed = 0f;
            float bounceTime = 0.3f;

            // Scale up
            while (elapsed < bounceTime / 2)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / (bounceTime / 2);
                m_TextComponent.transform.localScale = Vector3.Lerp(scaleGoc, scaleLon, t);
                yield return null;
            }

            // Scale down
            elapsed = 0f;
            while (elapsed < bounceTime / 2)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / (bounceTime / 2);
                m_TextComponent.transform.localScale = Vector3.Lerp(scaleLon, scaleGoc, t);
                yield return null;
            }

            m_TextComponent.transform.localScale = scaleGoc;
        }
        #endregion

        #region Editor Methods
        private void OnValidate()
        {
            if (m_ThoiGianHienThi < 0) m_ThoiGianHienThi = 0;
            if (m_ThoiGianFade < 0) m_ThoiGianFade = 0;
            if (m_KichThuocText < 1) m_KichThuocText = 1;
        }
        #endregion
    }
}
