using UnityEngine;

namespace PlayerSystem
{
    /// <summary>
    /// <PERSON>u<PERSON>n lý hiệu ứng visual và audio cho pickup system
    /// Bao gồm particle effects, sound effects và screen shake
    /// </summary>
    public class PickupEffects : MonoBehaviour
    {
        #region Serialized Fields
        [Header("✨ Particle Effects")]
        [Serial<PERSON>Field, <PERSON><PERSON><PERSON>("Particle effect khi pickup")]
        private ParticleSystem m_EffectPickup;
        
        [Ser<PERSON><PERSON>Field, Too<PERSON><PERSON>("Particle effect khi drop")]
        private ParticleSystem m_EffectDrop;
        
        [SerializeField, Tooltip("Particle effect khi highlight")]
        private ParticleSystem m_EffectHighlight;
        
        [SerializeF<PERSON>, Tooltip("Prefab particle effect pickup")]
        private GameObject m_PrefabEffectPickup;
        
        [Serial<PERSON>Field, Tooltip("Prefab particle effect drop")]
        private GameObject m_PrefabEffectDrop;

        [Header("🔊 Audio Effects")]
        [SerializeField, Toolt<PERSON>("Âm thanh pickup")]
        private AudioClip m_SoundPickup;
        
        [Serial<PERSON><PERSON>ield, <PERSON><PERSON><PERSON>("Âm thanh drop")]
        private AudioClip m_SoundDrop;
        
        [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Âm thanh highlight")]
        private AudioClip m_SoundHighlight;
        
        [Serial<PERSON>Field, Tooltip("Âm thanh không thể pickup")]
        private AudioClip m_SoundCannotPickup;
        
        [SerializeField, Range(0f, 1f), Tooltip("Âm lượng effects")]
        private float m_AmLuong = 0.7f;

        [Header("📳 Screen Shake")]
        [SerializeField, Tooltip("Có screen shake khi pickup")]
        private bool m_CoScreenShake = true;
        
        [SerializeField, Tooltip("Cường độ shake pickup")]
        private float m_CuongDoShakePickup = 0.1f;
        
        [SerializeField, Tooltip("Thời gian shake pickup")]
        private float m_ThoiGianShakePickup = 0.2f;
        
        [SerializeField, Tooltip("Cường độ shake drop")]
        private float m_CuongDoShakeDrop = 0.05f;
        
        [SerializeField, Tooltip("Thời gian shake drop")]
        private float m_ThoiGianShakeDrop = 0.1f;

        [Header("🎨 Visual Settings")]
        [SerializeField, Tooltip("Màu particle pickup")]
        private Color m_MauParticlePickup = Color.green;
        
        [SerializeField, Tooltip("Màu particle drop")]
        private Color m_MauParticleDrop = Color.blue;
        
        [SerializeField, Tooltip("Màu particle highlight")]
        private Color m_MauParticleHighlight = Color.yellow;
        
        [SerializeField, Tooltip("Số lượng particle")]
        private int m_SoLuongParticle = 20;
        
        [SerializeField, Tooltip("Tốc độ particle")]
        private float m_TocDoParticle = 5f;

        [Header("⚙️ Settings")]
        [SerializeField, Tooltip("Có tự động tạo effects")]
        private bool m_TuDongTaoEffects = true;
        
        [SerializeField, Tooltip("Thời gian sống của effect")]
        private float m_ThoiGianSongEffect = 2f;
        
        [SerializeField, Tooltip("Có sử dụng object pooling")]
        private bool m_SuDungObjectPooling = false;
        #endregion

        #region Private Fields
        private AudioSource m_AudioSource;
        private Camera m_PlayerCamera;
        private Transform m_CameraTransform;
        private Vector3 m_ViTriCameraBanDau;
        private bool m_DangShake = false;
        private float m_ShakeTimer = 0f;
        private float m_ShakeCuongDo = 0f;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            KhoiTaoComponent();
        }

        private void Start()
        {
            ThietLapEffects();
        }

        private void Update()
        {
            if (m_DangShake)
            {
                CapNhatScreenShake();
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Phát effect pickup tại vị trí chỉ định
        /// </summary>
        public void PlayPickupEffect(Vector3 position)
        {
            // Particle effect
            if (m_EffectPickup != null)
            {
                m_EffectPickup.transform.position = position;
                m_EffectPickup.Play();
            }
            else if (m_PrefabEffectPickup != null)
            {
                TaoEffectTuPrefab(m_PrefabEffectPickup, position);
            }
            else if (m_TuDongTaoEffects)
            {
                TaoEffectPickupTuDong(position);
            }

            // Sound effect
            PhatAmThanh(m_SoundPickup);

            // Screen shake
            if (m_CoScreenShake)
            {
                BatDauScreenShake(m_CuongDoShakePickup, m_ThoiGianShakePickup);
            }
        }

        /// <summary>
        /// Phát effect drop tại vị trí chỉ định
        /// </summary>
        public void PlayDropEffect(Vector3 position)
        {
            // Particle effect
            if (m_EffectDrop != null)
            {
                m_EffectDrop.transform.position = position;
                m_EffectDrop.Play();
            }
            else if (m_PrefabEffectDrop != null)
            {
                TaoEffectTuPrefab(m_PrefabEffectDrop, position);
            }
            else if (m_TuDongTaoEffects)
            {
                TaoEffectDropTuDong(position);
            }

            // Sound effect
            PhatAmThanh(m_SoundDrop);

            // Screen shake
            if (m_CoScreenShake)
            {
                BatDauScreenShake(m_CuongDoShakeDrop, m_ThoiGianShakeDrop);
            }
        }

        /// <summary>
        /// Phát effect highlight tại vị trí chỉ định
        /// </summary>
        public void PlayHighlightEffect(Vector3 position)
        {
            if (m_EffectHighlight != null)
            {
                m_EffectHighlight.transform.position = position;
                m_EffectHighlight.Play();
            }
            else if (m_TuDongTaoEffects)
            {
                TaoEffectHighlightTuDong(position);
            }

            PhatAmThanh(m_SoundHighlight);
        }

        /// <summary>
        /// Phát sound không thể pickup
        /// </summary>
        public void PlayCannotPickupSound()
        {
            PhatAmThanh(m_SoundCannotPickup);
        }

        /// <summary>
        /// Thiết lập âm lượng
        /// </summary>
        public void SetAmLuong(float amLuong)
        {
            m_AmLuong = Mathf.Clamp01(amLuong);
            if (m_AudioSource != null)
            {
                m_AudioSource.volume = m_AmLuong;
            }
        }
        #endregion

        #region Private Methods
        private void KhoiTaoComponent()
        {
            // Tạo AudioSource
            m_AudioSource = GetComponent<AudioSource>();
            if (m_AudioSource == null)
            {
                m_AudioSource = gameObject.AddComponent<AudioSource>();
                m_AudioSource.playOnAwake = false;
                m_AudioSource.spatialBlend = 0f; // 2D sound cho UI
            }

            // Tìm camera
            m_PlayerCamera = Camera.main;
            if (m_PlayerCamera == null)
            {
                m_PlayerCamera = FindObjectOfType<Camera>();
            }

            if (m_PlayerCamera != null)
            {
                m_CameraTransform = m_PlayerCamera.transform;
                m_ViTriCameraBanDau = m_CameraTransform.localPosition;
            }
        }

        private void ThietLapEffects()
        {
            m_AudioSource.volume = m_AmLuong;

            // Thiết lập particle systems nếu có
            ThietLapParticleSystem(m_EffectPickup, m_MauParticlePickup);
            ThietLapParticleSystem(m_EffectDrop, m_MauParticleDrop);
            ThietLapParticleSystem(m_EffectHighlight, m_MauParticleHighlight);
        }

        private void ThietLapParticleSystem(ParticleSystem ps, Color color)
        {
            if (ps == null) return;

            var main = ps.main;
            main.startColor = color;
            main.maxParticles = m_SoLuongParticle;
            main.startSpeed = m_TocDoParticle;
            main.startLifetime = m_ThoiGianSongEffect;
        }

        private void TaoEffectTuPrefab(GameObject prefab, Vector3 position)
        {
            GameObject effect = Instantiate(prefab, position, Quaternion.identity);
            
            if (!m_SuDungObjectPooling)
            {
                Destroy(effect, m_ThoiGianSongEffect);
            }
        }

        private void TaoEffectPickupTuDong(Vector3 position)
        {
            GameObject effectObj = new GameObject("PickupEffect");
            effectObj.transform.position = position;
            
            ParticleSystem ps = effectObj.AddComponent<ParticleSystem>();
            ThietLapParticleSystem(ps, m_MauParticlePickup);
            
            // Thiết lập shape
            var shape = ps.shape;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = 0.5f;
            
            ps.Play();
            Destroy(effectObj, m_ThoiGianSongEffect);
        }

        private void TaoEffectDropTuDong(Vector3 position)
        {
            GameObject effectObj = new GameObject("DropEffect");
            effectObj.transform.position = position;
            
            ParticleSystem ps = effectObj.AddComponent<ParticleSystem>();
            ThietLapParticleSystem(ps, m_MauParticleDrop);
            
            // Thiết lập shape
            var shape = ps.shape;
            shape.shapeType = ParticleSystemShapeType.Sphere;
            shape.radius = 0.3f;
            
            ps.Play();
            Destroy(effectObj, m_ThoiGianSongEffect);
        }

        private void TaoEffectHighlightTuDong(Vector3 position)
        {
            GameObject effectObj = new GameObject("HighlightEffect");
            effectObj.transform.position = position;
            
            ParticleSystem ps = effectObj.AddComponent<ParticleSystem>();
            ThietLapParticleSystem(ps, m_MauParticleHighlight);
            
            // Thiết lập cho highlight effect
            var main = ps.main;
            main.loop = true;
            main.startLifetime = 0.5f;
            
            var emission = ps.emission;
            emission.rateOverTime = 10f;
            
            ps.Play();
            Destroy(effectObj, 1f);
        }

        private void PhatAmThanh(AudioClip clip)
        {
            if (m_AudioSource != null && clip != null)
            {
                m_AudioSource.PlayOneShot(clip, m_AmLuong);
            }
        }

        private void BatDauScreenShake(float cuongDo, float thoiGian)
        {
            if (m_CameraTransform == null) return;

            m_DangShake = true;
            m_ShakeTimer = thoiGian;
            m_ShakeCuongDo = cuongDo;
        }

        private void CapNhatScreenShake()
        {
            if (m_CameraTransform == null)
            {
                m_DangShake = false;
                return;
            }

            m_ShakeTimer -= Time.deltaTime;

            if (m_ShakeTimer <= 0f)
            {
                // Kết thúc shake
                m_DangShake = false;
                m_CameraTransform.localPosition = m_ViTriCameraBanDau;
            }
            else
            {
                // Tính toán shake
                float shakeAmount = m_ShakeCuongDo * (m_ShakeTimer / m_ThoiGianShakePickup);
                Vector3 shakeOffset = Random.insideUnitSphere * shakeAmount;
                shakeOffset.z = 0f; // Không shake theo trục Z
                
                m_CameraTransform.localPosition = m_ViTriCameraBanDau + shakeOffset;
            }
        }
        #endregion

        #region Debug
        private void OnDrawGizmosSelected()
        {
            // Vẽ thông tin debug cho effects
            if (m_EffectPickup != null)
            {
                Gizmos.color = m_MauParticlePickup;
                Gizmos.DrawWireSphere(m_EffectPickup.transform.position, 0.5f);
            }
            
            if (m_EffectDrop != null)
            {
                Gizmos.color = m_MauParticleDrop;
                Gizmos.DrawWireSphere(m_EffectDrop.transform.position, 0.3f);
            }
        }
        #endregion
    }
}
