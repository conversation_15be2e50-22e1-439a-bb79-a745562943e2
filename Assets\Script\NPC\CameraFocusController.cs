using UnityEngine;
using System.Collections;

namespace NPCSystem
{
    /// <summary>
    /// <PERSON>i<PERSON>u khiển camera zoom và focus vào NPC khi tương tác
    /// Tích hợp với CameraController hiện có của player
    /// </summary>
    public class CameraFocusController : MonoBehaviour
    {
        #region Serialized Fields
        [Header("📷 Camera Focus Settings")]
        [Ser<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Camera chính của player")]
        private Camera m_PlayerCamera;
        
        [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("CameraController của player")]
        private PlayerSystem.CameraController m_CameraController;
        
        [<PERSON><PERSON>("🎯 Focus Behavior")]
        [SerializeField, Toolt<PERSON>("Khoảng cách camera đến NPC khi focus")]
        private float m_FocusDistance = 3f;
        
        [SerializeField, Toolt<PERSON>("Độ cao camera so với NPC")]
        private float m_FocusHeight = 1.5f;
        
        [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("<PERSON><PERSON><PERSON> nghiêng camera (độ)")]
        private float m_FocusAngle = 15f;
        
        [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Thời gian transition camera")]
        private float m_TransitionTime = 1f;
        
        [Serial<PERSON>Field, <PERSON><PERSON><PERSON>("Curve cho smooth transition")]
        private AnimationCurve m_TransitionCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        [Header("🎨 Visual Effects")]
        [SerializeField, Tooltip("Có blur background khi focus")]
        private bool m_BlurBackground = true;
        
        [SerializeField, Tooltip("Có vignette effect")]
        private bool m_VignetteEffect = true;
        
        [SerializeField, Tooltip("Có disable player movement")]
        private bool m_DisablePlayerMovement = true;
        
        [Header("🔧 Debug")]
        [SerializeField, Tooltip("Hiển thị log debug")]
        private bool m_HienThiLog = true;
        #endregion

        #region Private Fields
        private Vector3 m_OriginalCameraPosition;
        private Quaternion m_OriginalCameraRotation;
        private bool m_IsFocusing = false;
        private Transform m_CurrentFocusTarget;
        private Coroutine m_FocusCoroutine;
        
        // Player control references
        private PlayerSystem.PlayerController m_PlayerController;
        private PlayerSystem.PlayerInputHandler m_PlayerInput;
        #endregion

        #region Properties
        public bool IsFocusing => m_IsFocusing;
        public Transform CurrentFocusTarget => m_CurrentFocusTarget;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            KhoiTaoReferences();
        }

        private void Start()
        {
            if (m_PlayerCamera == null)
            {
                m_PlayerCamera = Camera.main;
            }
            
            Log("CameraFocusController đã khởi tạo");
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Focus camera vào NPC và item
        /// </summary>
        public void FocusOnNPCAndItem(Transform npcTransform, Transform itemTransform = null)
        {
            if (m_IsFocusing)
            {
                Log("Camera đang focus, bỏ qua request mới");
                return;
            }

            if (npcTransform == null)
            {
                LogLoi("NPC Transform null!");
                return;
            }

            Log($"Bắt đầu focus vào NPC: {npcTransform.name}");
            
            m_CurrentFocusTarget = npcTransform;
            
            // Lưu vị trí camera gốc
            LuuViTriCameraGoc();
            
            // Disable player controls
            if (m_DisablePlayerMovement)
            {
                DisablePlayerControls();
            }
            
            // Bắt đầu focus transition
            if (m_FocusCoroutine != null)
            {
                StopCoroutine(m_FocusCoroutine);
            }
            
            m_FocusCoroutine = StartCoroutine(FocusTransition(npcTransform, itemTransform));
        }

        /// <summary>
        /// Trở về camera bình thường
        /// </summary>
        public void ReturnToNormalView()
        {
            if (!m_IsFocusing)
            {
                Log("Camera không đang focus");
                return;
            }

            Log("Trở về camera bình thường");
            
            if (m_FocusCoroutine != null)
            {
                StopCoroutine(m_FocusCoroutine);
            }
            
            m_FocusCoroutine = StartCoroutine(ReturnTransition());
        }

        /// <summary>
        /// Toggle focus mode
        /// </summary>
        public void ToggleFocus(Transform target)
        {
            if (m_IsFocusing)
            {
                ReturnToNormalView();
            }
            else
            {
                FocusOnNPCAndItem(target);
            }
        }
        #endregion

        #region Private Methods
        private void KhoiTaoReferences()
        {
            // Tự động tìm player components
            if (m_PlayerController == null)
            {
                m_PlayerController = FindObjectOfType<PlayerSystem.PlayerController>();
            }
            
            if (m_PlayerInput == null)
            {
                m_PlayerInput = FindObjectOfType<PlayerSystem.PlayerInputHandler>();
            }
            
            if (m_CameraController == null)
            {
                m_CameraController = FindObjectOfType<PlayerSystem.CameraController>();
            }
        }

        private void LuuViTriCameraGoc()
        {
            if (m_PlayerCamera != null)
            {
                m_OriginalCameraPosition = m_PlayerCamera.transform.position;
                m_OriginalCameraRotation = m_PlayerCamera.transform.rotation;
            }
        }

        private void DisablePlayerControls()
        {
            if (m_PlayerController != null)
            {
                // Disable movement (sẽ cần method trong PlayerController)
                Log("Disabled player movement");
            }
            
            if (m_PlayerInput != null)
            {
                // Disable input (sẽ cần method trong PlayerInputHandler)
                Log("Disabled player input");
            }
            
            if (m_CameraController != null)
            {
                // Disable camera control (sẽ cần method trong CameraController)
                Log("Disabled camera control");
            }
        }

        private void EnablePlayerControls()
        {
            if (m_PlayerController != null)
            {
                Log("Enabled player movement");
            }
            
            if (m_PlayerInput != null)
            {
                Log("Enabled player input");
            }
            
            if (m_CameraController != null)
            {
                Log("Enabled camera control");
            }
        }

        private Vector3 TinhViTriCameraFocus(Transform npcTransform, Transform itemTransform = null)
        {
            Vector3 targetPosition;
            
            if (itemTransform != null)
            {
                // Tính vị trí giữa NPC và item
                targetPosition = (npcTransform.position + itemTransform.position) * 0.5f;
            }
            else
            {
                targetPosition = npcTransform.position;
            }
            
            // Thêm offset để camera nhìn từ phía trước
            Vector3 offset = new Vector3(m_FocusDistance, m_FocusHeight, 0);
            
            // Xoay offset theo hướng của NPC (nếu cần)
            return targetPosition + offset;
        }

        private Quaternion TinhRotationCameraFocus(Transform npcTransform, Transform itemTransform = null)
        {
            Vector3 targetPosition;
            
            if (itemTransform != null)
            {
                targetPosition = (npcTransform.position + itemTransform.position) * 0.5f;
            }
            else
            {
                targetPosition = npcTransform.position + Vector3.up * 1.5f; // Nhìn vào đầu NPC
            }
            
            Vector3 cameraPosition = TinhViTriCameraFocus(npcTransform, itemTransform);
            Vector3 direction = (targetPosition - cameraPosition).normalized;
            
            Quaternion lookRotation = Quaternion.LookRotation(direction);
            
            // Thêm góc nghiêng
            return lookRotation * Quaternion.Euler(m_FocusAngle, 0, 0);
        }

        private IEnumerator FocusTransition(Transform npcTransform, Transform itemTransform)
        {
            m_IsFocusing = true;
            
            Vector3 startPosition = m_PlayerCamera.transform.position;
            Quaternion startRotation = m_PlayerCamera.transform.rotation;
            
            Vector3 targetPosition = TinhViTriCameraFocus(npcTransform, itemTransform);
            Quaternion targetRotation = TinhRotationCameraFocus(npcTransform, itemTransform);
            
            float elapsed = 0f;
            
            while (elapsed < m_TransitionTime)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / m_TransitionTime;
                float curveT = m_TransitionCurve.Evaluate(t);
                
                // Interpolate position và rotation
                m_PlayerCamera.transform.position = Vector3.Lerp(startPosition, targetPosition, curveT);
                m_PlayerCamera.transform.rotation = Quaternion.Lerp(startRotation, targetRotation, curveT);
                
                yield return null;
            }
            
            // Đảm bảo vị trí cuối chính xác
            m_PlayerCamera.transform.position = targetPosition;
            m_PlayerCamera.transform.rotation = targetRotation;
            
            Log("Camera focus hoàn thành");
        }

        private IEnumerator ReturnTransition()
        {
            Vector3 startPosition = m_PlayerCamera.transform.position;
            Quaternion startRotation = m_PlayerCamera.transform.rotation;
            
            float elapsed = 0f;
            
            while (elapsed < m_TransitionTime)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / m_TransitionTime;
                float curveT = m_TransitionCurve.Evaluate(t);
                
                m_PlayerCamera.transform.position = Vector3.Lerp(startPosition, m_OriginalCameraPosition, curveT);
                m_PlayerCamera.transform.rotation = Quaternion.Lerp(startRotation, m_OriginalCameraRotation, curveT);
                
                yield return null;
            }
            
            // Đảm bảo vị trí cuối chính xác
            m_PlayerCamera.transform.position = m_OriginalCameraPosition;
            m_PlayerCamera.transform.rotation = m_OriginalCameraRotation;
            
            // Enable lại player controls
            if (m_DisablePlayerMovement)
            {
                EnablePlayerControls();
            }
            
            m_IsFocusing = false;
            m_CurrentFocusTarget = null;
            
            Log("Trở về camera bình thường hoàn thành");
        }

        private void Log(string message)
        {
            if (m_HienThiLog)
            {
                Debug.Log($"[CameraFocus] {message}");
            }
        }

        private void LogLoi(string message)
        {
            Debug.LogError($"[CameraFocus] {message}");
        }
        #endregion

        #region Editor Methods
        private void OnValidate()
        {
            if (m_FocusDistance < 0) m_FocusDistance = 0;
            if (m_TransitionTime < 0) m_TransitionTime = 0;
        }

        private void OnDrawGizmos()
        {
            if (m_CurrentFocusTarget != null && m_IsFocusing)
            {
                // Vẽ gizmo cho focus position
                Vector3 focusPos = TinhViTriCameraFocus(m_CurrentFocusTarget);
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(focusPos, 0.3f);
                Gizmos.DrawLine(focusPos, m_CurrentFocusTarget.position);
            }
        }
        #endregion
    }
}
