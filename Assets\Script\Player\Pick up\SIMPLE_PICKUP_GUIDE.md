# 🎯 HỆ THỐNG PICKUP ĐỠN GIẢN

## 📋 Tổng Quan

Hệ thống pickup cực kỳ đơn giản với chỉ những tính năng cơ bản:
- ✅ **Nhấn E để nhặt** vật gần nhất
- ✅ **Nhấn E lại để thả** vật
- ✅ **Vật giữ chặt** ở vị trí cố định (không bay lơ lửng)
- ✅ **Hover text** "Press E to pick up" khi nhìn vào vật
- ✅ **Tự động tắt physics** khi nhặt

---

## 🚀 Cài Đặt Nhanh (2 Phút)

### Bước 1: Setup Player
1. Chọn **Player GameObject** trong Hierarchy
2. Add Component → **PickupHelper**
3. **Điều chỉnh settings** trong PickupHelper Inspector:
   - Pickup Range, Carry Position, Drop Settings
4. Nhấn **"Setup Pickup System"** trong Context Menu (Right-click component)

### Bước 2: Tạo Vật Phẩm Test
1. Nhấn **"Create Test Item"** trong Context Menu của PickupHelper
2. Hoặc nhấn **"Create Multiple Test Items"** để tạo 5 vật màu sắc khác nhau

### Bước 3: Test
1. **Chạy game** (Play button)
2. **Nhìn vào vật phẩm** → Thấy text "Press E to pick up"
3. **Nhấn E** để nhặt → Vật sẽ giữ chặt ở vị trí cố định
4. **Nhấn E lại** để thả vật

**🎉 Xong! Vật giờ sẽ không bay lơ lửng nữa!**

---

## 📁 Cấu Trúc Files

```
Assets/Script/Player/Pick up/
├── SimplePickup.cs          # Script chính - nhặt và thả
├── PickupableItem.cs        # Component cho vật phẩm
├── PickupHelper.cs          # Helper setup nhanh
└── SIMPLE_PICKUP_GUIDE.md   # File này
```

---

## ⚙️ Cài Đặt Chi Tiết

### PickupHelper Component (Điều chỉnh chính ở đây)
- **Pickup Range**: Khoảng cách nhặt (3m)
- **Pickup Key**: Phím nhặt (E)
- **Carry Distance**: Khoảng cách phía trước camera (1.5m)
- **Carry Height**: Độ cao so với camera (-0.3m = thấp hơn)
- **Carry Side Offset**: Vị trí trái/phải (0.3m = bên phải)
- **Drop Distance**: Khoảng cách thả phía trước (1.5m)
- **Drop Height**: Độ cao thả so với mặt đất (0.5m)
- **Check Drop Collision**: Kiểm tra va chạm khi thả

### SimplePickup Component (Tự động cập nhật từ PickupHelper)
- Các settings sẽ được copy từ PickupHelper
- Nhấn **"Apply Settings to Pickup System"** để cập nhật

### PickupableItem Component
- **Item Name**: Tên vật phẩm
- **Can Be Picked Up**: Có thể nhặt hay không
- **Enable Floating**: Hiệu ứng lơ lửng (mặc định: TẮT)
- **Float Speed**: Tốc độ lơ lửng
- **Enable Rotation**: Hiệu ứng xoay (mặc định: TẮT)

---

## 🎮 Cách Sử Dụng

### Trong Game
- **Nhặt**: Nhìn vào vật → Nhấn E
- **Thả**: Khi đang mang vật → Nhấn E

### Tạo Vật Phẩm Mới
1. Tạo GameObject bất kỳ (Cube, Sphere, etc.)
2. Add Component → **PickupableItem**
3. Vật sẽ tự động có tag "Pickupable"

### Setup Vật Có Sẵn
1. Chọn GameObject muốn làm pickup
2. Trong PickupHelper, nhấn **"Setup Selected Object as Pickupable"**

---

## 🔧 Tùy Chỉnh

### Thay Đổi Phím Pickup
```csharp
// Trong SimplePickup component
Pickup Key = F  // Thay E thành F
```

### Điều Chỉnh Vị Trí Pickup
```csharp
// Trong SimplePickup component
Carry Distance = 1.5f     // Khoảng cách phía trước
Carry Height = -0.3f      // Độ cao (âm = thấp hơn camera)
Carry Side Offset = 0.3f  // Vị trí trái/phải (dương = phải)
```

### Tắt Hiệu Ứng (Đã tắt mặc định)
```csharp
// Trong PickupableItem component
Enable Floating = false   // Tắt lơ lửng
Enable Rotation = false   // Tắt xoay
```

### Cập Nhật Vị Trí Real-time
- Bật **"Update Position In Real Time"** để thấy thay đổi ngay trong Editor
- Hoặc Right-click component → **"Update Carry Position"**

---

## 🔍 Troubleshooting

### Không Nhặt Được
- ✅ Kiểm tra vật có tag "Pickupable"
- ✅ Kiểm tra vật có Collider
- ✅ Kiểm tra khoảng cách (< 3m)
- ✅ Kiểm tra nhìn đúng hướng vật

### Vật Không Theo
- ✅ Kiểm tra Follow Speed > 0
- ✅ Kiểm tra Carry Position đã được tạo

### Lỗi Script
- ✅ Đảm bảo tất cả 3 files đã được tạo
- ✅ Restart Unity nếu cần
- ✅ Kiểm tra Console có lỗi gì

### Vật Thả Sai Hướng
- ✅ **Đã sửa**: Vật giờ thả theo hướng camera thay vì player transform
- ✅ Điều chỉnh **Drop Distance** và **Drop Height** trong PickupHelper
- ✅ Bật **Check Drop Collision** để tránh thả vào tường
- ✅ Dùng **"Test Drop Position"** trong Context Menu để debug

---

## 📝 Ghi Chú

- **Đơn giản**: Chỉ 3 scripts, không có UI phức tạp
- **Nhẹ**: Không ảnh hưởng performance
- **Dễ tùy chỉnh**: Tất cả settings trong Inspector
- **Tương thích**: Hoạt động với mọi GameObject

**Hệ thống này hoàn hảo cho game đơn giản cần pickup cơ bản!** 🎮
