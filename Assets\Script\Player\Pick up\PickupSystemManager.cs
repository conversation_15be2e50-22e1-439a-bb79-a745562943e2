using UnityEngine;

/// <summary>
/// Manager ch<PERSON>h cho hệ thống pickup
/// Qu<PERSON>n lý và điều phối tất cả các component pickup
/// </summary>
public class PickupSystemManager : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎮 System Components")]
        [SerializeField, <PERSON><PERSON><PERSON>("Pickup System chính")]
        private PlayerSystem.PickupSystem m_PickupSystem;

        [SerializeField, Toolt<PERSON>("UI hiển thị pickup")]
        private PlayerSystem.PickupUI m_PickupUI;

        [SerializeField, Tooltip("Effects cho pickup")]
        private PlayerSystem.PickupEffects m_PickupEffects;

        [Header("🔧 External References")]
        [SerializeF<PERSON>, Toolt<PERSON>("Player Inventory (tự động tìm)")]
        private MonoBehaviour m_PlayerInventory; // Sẽ cast thành PlayerInventory sau
        
        [SerializeField, <PERSON><PERSON><PERSON>("Economy System Manager (tự động tìm)")]
        private MonoBehaviour m_EconomySystemManager;

        [Header("⚙️ Auto Setup")]
        [Serial<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Tự động thiết lập khi Start")]
        private bool m_TuDongThietLap = true;
        
        [SerializeField, Tooltip("Tự động tìm references")]
        private bool m_TuDongTimReferences = true;
        
        [SerializeField, Tooltip("Hiển thị debug logs")]
        private bool m_HienThiDebugLogs = true;

        [Header("🎯 Pickup Settings")]
        [SerializeField, Tooltip("Prefab mặc định cho pickup items")]
        private GameObject m_PrefabPickupItem;
        
        [SerializeField, Tooltip("Layer cho pickup items")]
        private int m_LayerPickupItems = 0;
        
        [SerializeField, Tooltip("Tag mặc định cho pickup items")]
        private string m_TagMacDinh = "Pickupable";
        #endregion

        #region Properties
        /// <summary>Pickup System component</summary>
        public PlayerSystem.PickupSystem PickupSystem => m_PickupSystem;

        /// <summary>Pickup UI component</summary>
        public PlayerSystem.PickupUI PickupUI => m_PickupUI;

        /// <summary>Pickup Effects component</summary>
        public PlayerSystem.PickupEffects PickupEffects => m_PickupEffects;
        
        /// <summary>Player Inventory reference</summary>
        public MonoBehaviour PlayerInventory => m_PlayerInventory;
        
        /// <summary>Có đang mang vật phẩm không</summary>
        public bool IsCarryingItems() => m_PickupSystem != null && m_PickupSystem.IsCarryingItems;
        
        /// <summary>Số lượng vật phẩm đang mang</summary>
        public int GetCarriedItemsCount() => m_PickupSystem?.CarriedItemsCount ?? 0;
        
        /// <summary>Vật phẩm hiện tại được phát hiện</summary>
        public PlayerSystem.PickupItem GetCurrentDetectedItem() => m_PickupSystem?.CurrentDetectedItem;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (m_TuDongTimReferences)
            {
                TimTatCaReferences();
            }
        }

        private void Start()
        {
            if (m_TuDongThietLap)
            {
                ThietLapHeThong();
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Thiết lập hệ thống pickup thủ công
        /// </summary>
        public void ThietLapHeThong()
        {
            // Đảm bảo có tất cả components cần thiết
            DamBaoCoTatCaComponents();
            
            // Thiết lập references
            ThietLapReferences();
            
            // Validate setup
            bool isValid = ValidateSetup();
            
            if (m_HienThiDebugLogs)
            {
                string status = isValid ? "✅ Thành công" : "❌ Có lỗi";
                Debug.Log($"[PickupSystemManager] Thiết lập hệ thống: {status}");
            }
        }

        /// <summary>
        /// Tạo vật phẩm pickup tại vị trí chỉ định
        /// </summary>
        public GameObject TaoVatPhamPickup(Vector3 position, string itemId, int soLuong, string tenHienThi, string moTa)
        {
            GameObject pickupObj;
            
            if (m_PrefabPickupItem != null)
            {
                pickupObj = Instantiate(m_PrefabPickupItem, position, Quaternion.identity);
            }
            else
            {
                // Tạo cube đơn giản
                pickupObj = GameObject.CreatePrimitive(PrimitiveType.Cube);
                pickupObj.transform.position = position;
                pickupObj.name = $"PickupItem_{itemId}";
            }

            // Thêm PickupItem component
            PlayerSystem.PickupItem pickupItem = pickupObj.GetComponent<PlayerSystem.PickupItem>();
            if (pickupItem == null)
            {
                pickupItem = pickupObj.AddComponent<PlayerSystem.PickupItem>();
            }

            // Thiết lập thông tin
            pickupItem.ThietLapVatPham(itemId, soLuong, tenHienThi, moTa);
            
            // Thiết lập tag và layer
            pickupObj.tag = m_TagMacDinh;
            pickupObj.layer = m_LayerPickupItems;

            if (m_HienThiDebugLogs)
            {
                Debug.Log($"[PickupSystemManager] Đã tạo vật phẩm: {tenHienThi} tại {position}");
            }

            return pickupObj;
        }

        /// <summary>
        /// Thiết lập GameObject thành pickup item
        /// </summary>
        public PlayerSystem.PickupItem ThietLapPickupChoGameObject(GameObject obj, string itemId, int soLuong, string tenHienThi, string moTa)
        {
            if (obj == null) return null;

            // Thêm PickupItem component
            PlayerSystem.PickupItem pickupItem = obj.GetComponent<PlayerSystem.PickupItem>();
            if (pickupItem == null)
            {
                pickupItem = obj.AddComponent<PlayerSystem.PickupItem>();
            }

            // Đảm bảo có Collider
            if (obj.GetComponent<Collider>() == null)
            {
                obj.AddComponent<BoxCollider>();
            }

            // Thiết lập thông tin
            pickupItem.ThietLapVatPham(itemId, soLuong, tenHienThi, moTa);
            
            // Thiết lập tag và layer
            obj.tag = m_TagMacDinh;
            obj.layer = m_LayerPickupItems;

            if (m_HienThiDebugLogs)
            {
                Debug.Log($"[PickupSystemManager] Đã thiết lập {obj.name} thành pickup item: {tenHienThi}");
            }

            return pickupItem;
        }

        /// <summary>
        /// Force pickup một vật phẩm
        /// </summary>
        public bool ForcePickupItem(PlayerSystem.PickupItem item)
        {
            if (m_PickupSystem == null || item == null) return false;
            
            return m_PickupSystem.TryPickupItem(item);
        }

        /// <summary>
        /// Validate setup của hệ thống
        /// </summary>
        public bool ValidateSetup()
        {
            bool isValid = true;
            
            if (m_PickupSystem == null)
            {
                Debug.LogWarning("[PickupSystemManager] Thiếu PickupSystem component!");
                isValid = false;
            }
            
            if (m_PickupUI == null)
            {
                Debug.LogWarning("[PickupSystemManager] Thiếu PickupUI component!");
                isValid = false;
            }
            
            if (m_PickupEffects == null)
            {
                Debug.LogWarning("[PickupSystemManager] Thiếu PickupEffects component!");
                isValid = false;
            }

            return isValid;
        }
        #endregion

        #region Private Methods
        private void TimTatCaReferences()
        {
            // Tìm PickupSystem
            if (m_PickupSystem == null)
            {
                m_PickupSystem = GetComponent<PlayerSystem.PickupSystem>();
            }

            // Tìm PickupUI
            if (m_PickupUI == null)
            {
                m_PickupUI = FindObjectOfType<PlayerSystem.PickupUI>();
            }

            // Tìm PickupEffects
            if (m_PickupEffects == null)
            {
                m_PickupEffects = GetComponent<PlayerSystem.PickupEffects>();
            }

            // Tìm PlayerInventory (sẽ tích hợp với EconomySystem sau)
            if (m_PlayerInventory == null)
            {
                // Tìm trong EconomySystem namespace
                var inventoryType = System.Type.GetType("EconomySystem.PlayerInventory");
                if (inventoryType != null)
                {
                    m_PlayerInventory = FindObjectOfType(inventoryType) as MonoBehaviour;
                }
            }

            // Tìm EconomySystemManager
            if (m_EconomySystemManager == null)
            {
                var economyManagerType = System.Type.GetType("EconomySystem.EconomySystemManager");
                if (economyManagerType != null)
                {
                    m_EconomySystemManager = FindObjectOfType(economyManagerType) as MonoBehaviour;
                }
            }
        }

        private void DamBaoCoTatCaComponents()
        {
            // Tạo PickupSystem nếu chưa có
            if (m_PickupSystem == null)
            {
                m_PickupSystem = gameObject.AddComponent<PlayerSystem.PickupSystem>();
                if (m_HienThiDebugLogs)
                {
                    Debug.Log("[PickupSystemManager] Đã tạo PickupSystem component");
                }
            }

            // Tạo PickupEffects nếu chưa có
            if (m_PickupEffects == null)
            {
                m_PickupEffects = gameObject.AddComponent<PlayerSystem.PickupEffects>();
                if (m_HienThiDebugLogs)
                {
                    Debug.Log("[PickupSystemManager] Đã tạo PickupEffects component");
                }
            }
        }

        private void ThietLapReferences()
        {
            // Thiết lập references cho PickupSystem
            if (m_PickupSystem != null)
            {
                // Sử dụng reflection để set private fields nếu cần
                // Hoặc tạo public methods trong PickupSystem để set references
            }
        }
        #endregion

        #region Debug
        [ContextMenu("Log System Status")]
        private void LogSystemStatus()
        {
            string status = "=== PICKUP SYSTEM STATUS ===\n";
            status += $"PickupSystem: {(m_PickupSystem != null ? "✅" : "❌")}\n";
            status += $"PickupUI: {(m_PickupUI != null ? "✅" : "❌")}\n";
            status += $"PickupEffects: {(m_PickupEffects != null ? "✅" : "❌")}\n";
            status += $"PlayerInventory: {(m_PlayerInventory != null ? "✅" : "❌")}\n";
            
            if (Application.isPlaying)
            {
                status += $"Is Carrying: {IsCarryingItems()}\n";
                status += $"Carried Count: {GetCarriedItemsCount()}\n";
                var currentItem = GetCurrentDetectedItem();
                status += $"Current Detected: {(currentItem?.TenHienThi ?? "None")}\n";
            }

            Debug.Log(status);
        }

        [ContextMenu("Create Test Pickup Item")]
        private void CreateTestPickupItem()
        {
            Vector3 position = transform.position + transform.forward * 2f;
            TaoVatPhamPickup(position, "test_item", 1, "Test Item", "Vật phẩm test cho pickup system");
        }
        #endregion
    }
