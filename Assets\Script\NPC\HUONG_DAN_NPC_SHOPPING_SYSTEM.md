# 🛍️ Hướng Dẫn NPC Shopping System

## 📋 Tổng Quan

Hệ thống AI NPC Shopping cho phép tạo ra các NPC thông minh có thể:
- 🚶 Di chuyển tự động đến các vùng trưng bày hàng hóa
- 💰 Kiểm tra tiền và quyết định mua hàng
- 💬 Hiển thị "I want to buy..." trên đầu
- 🎯 Chờ player click để tương tác
- 📷 Trigger camera zoom và hội thoại

---

## 🚀 Thiết Lập Cơ Bản

### Bước 1: Tạo Shop Display Zone

1. **Tạo GameObject mới:**
   ```
   GameObject → Create Empty → Đặt tên "ShopZone_Wood"
   ```

2. **Thêm Component:**
   ```
   Add Component → ShopDisplayZone
   ```

3. **Cấu hình trong Inspector:**
   ```
   Item ID: "wood"
   Tên Hiển Thị: "Wood"
   Gi<PERSON>: 50
   Kích Thước Zone: (2, 2, 2)
   ```

4. **Đặt 3D Model hàng hóa:**
   - Kéo 3D model gỗ vào scene
   - Gán vào field "Item Display"

### Bước 2: Tạo NPC Shopping AI

1. **Tạo NPC GameObject:**
   ```
   GameObject → 3D Object → Capsule → Đặt tên "ShoppingNPC"
   ```

2. **Thêm các Components cần thiết:**
   ```
   Add Component → Nav Mesh Agent
   Add Component → NPCCurrencyManager  
   Add Component → NPCShoppingAI
   ```

3. **Cấu hình NPCCurrencyManager:**
   ```
   Số Tiền Ban Đầu: 1000
   Số Tiền Tối Thiểu: 100
   Phần Trăm Chi Tiêu: 0.3 (30%)
   ```

4. **Cấu hình NPCShoppingAI:**
   ```
   Tên NPC: "Shopping Bot"
   Tốc Độ Chuyển Động: 3.5
   Bán Kính Tìm Kiếm: 20
   Thời Gian Chờ Player: 15
   ```

### Bước 3: Tạo World Space Text Display

1. **Tạo Canvas cho NPC:**
   ```
   Right-click NPC → UI → Canvas
   Đặt tên: "NPCTextCanvas"
   ```

2. **Cấu hình Canvas:**
   ```
   Render Mode: World Space
   Canvas Scaler → UI Scale Mode: Scale With Screen Size
   ```

3. **Tạo Text:**
   ```
   Right-click Canvas → UI → Text - TextMeshPro
   Đặt tên: "NPCText"
   ```

4. **Thêm WorldSpaceTextDisplay:**
   ```
   Add Component → WorldSpaceTextDisplay (vào Canvas)
   ```

5. **Gán References:**
   ```
   Text Component: NPCText
   Canvas: NPCTextCanvas
   Offset Position: (0, 2.5, 0)
   ```

6. **Gán vào NPCShoppingAI:**
   ```
   Text Display: NPCTextCanvas (GameObject có WorldSpaceTextDisplay)
   ```

### Bước 4: Tạo Camera Focus Controller

1. **Tạo CameraFocus GameObject:**
   ```
   GameObject → Create Empty → Đặt tên "CameraFocusController"
   ```

2. **Thêm Component:**
   ```
   Add Component → CameraFocusController
   ```

3. **Cấu hình trong Inspector:**
   ```
   Player Camera: Main Camera
   Focus Distance: 3
   Focus Height: 1.5
   Transition Time: 1
   Disable Player Movement: true
   ```

### Bước 5: Tạo Dialogue System

1. **Tạo Dialogue UI Canvas:**
   ```
   GameObject → UI → Canvas → Đặt tên "DialogueCanvas"
   ```

2. **Cấu hình Canvas:**
   ```
   Render Mode: Screen Space - Overlay
   Canvas Scaler → UI Scale Mode: Scale With Screen Size
   Reference Resolution: 1920x1080
   ```

3. **Tạo Dialogue Panel:**
   ```
   Right-click DialogueCanvas → UI → Panel → Đặt tên "DialoguePanel"
   ```

4. **Tạo UI Elements:**
   ```
   - NPC Name Text (TextMeshPro)
   - Dialogue Text (TextMeshPro)
   - Choice Buttons Parent (Empty GameObject)
   ```

5. **Tạo Choice Button Prefab:**
   ```
   Right-click Choice Buttons Parent → UI → Button → Đặt tên "ChoiceButton"
   Kéo thành Prefab vào Project
   ```

6. **Thêm NPCDialogueSystem:**
   ```
   Add Component → NPCDialogueSystem (vào DialogueCanvas)
   ```

7. **Gán References:**
   ```
   Dialogue Canvas: DialogueCanvas
   Dialogue Panel: DialoguePanel
   NPC Name Text: [Text component]
   Dialogue Text: [Text component]
   Choice Buttons Parent: [Empty GameObject]
   Choice Button Prefab: [Button Prefab]
   ```

---

## 🎮 Cách Sử Dụng

### Trong Game:

1. **NPC tự động hoạt động:**
   - NPC sẽ đi lang thang trong scene
   - Tìm kiếm shop zones trong bán kính
   - Kiểm tra đủ tiền mua hàng

2. **Khi NPC muốn mua:**
   - Di chuyển đến shop zone
   - Đứng ở vị trí xem hàng
   - Hiển thị "I want to buy [item] (price Lea)"

3. **Player tương tác:**
   - Click vào NPC khi có text hiển thị
   - Camera zoom vào NPC và item ✅
   - Hệ thống hội thoại xuất hiện ✅
   - Player chọn lựa chọn hội thoại
   - Giao dịch hoàn thành hoặc hủy bỏ

---

## 🔧 Cấu Hình Nâng Cao

### NavMesh Setup:

1. **Bake NavMesh:**
   ```
   Window → AI → Navigation
   Chọn tất cả static objects
   Bake
   ```

2. **Kiểm tra NPC có NavMeshAgent:**
   ```
   Agent Type: Humanoid
   Base Offset: 0
   Speed: 3.5
   Stopping Distance: 1
   ```

### Tùy Chỉnh Hành Vi:

```csharp
// Trong NPCShoppingAI Inspector:
Thời Gian Chờ Tìm Shop: 10s    // Tần suất tìm shop
Bán Kính Tìm Kiếm: 20          // Khoảng cách tìm shop
Thời Gian Chờ Player: 15s      // Timeout chờ player click
```

### Tùy Chỉnh Tiền Tệ:

```csharp
// Trong NPCCurrencyManager Inspector:
Số Tiền Ban Đầu: 1000          // Tiền khởi tạo
Phần Trăm Chi Tiêu: 0.3        // 30% tổng tiền dư thừa
Thời Gian Cooldown: 30s        // Thời gian giữa các lần mua
```

---

## 🎨 Tùy Chỉnh UI Text

### Trong WorldSpaceTextDisplay:

```csharp
Kích Thước Text: 24
Màu Text Mặc Định: White
Thời Gian Hiển Thị: 5s
Có Hiệu Ứng Fade: true
Có Hiệu Ứng Bounce: true
```

### Tùy Chỉnh Vị Trí Text:

```csharp
Offset Position: (0, 2.5, 0)   // Cao hơn đầu NPC
Luôn Nhìn Camera: true         // Text luôn quay về camera
```

---

## 🔍 Debug và Kiểm Tra

### Bước 6: Thêm Debug Helper (QUAN TRỌNG!)

**Chọn 1 trong 2 cách:**

**Cách 1: NPCShoppingTester (Đơn giản - Khuyến nghị)**
1. **Tạo Debug GameObject:**
   ```
   GameObject → Create Empty → Đặt tên "NPCShoppingTester"
   ```

2. **Thêm Component:**
   ```
   Add Component → NPCShoppingTester
   ```

3. **Cấu hình:**
   ```
   Target NPC: [Kéo NPC vào đây]
   Target Shop: [Kéo Shop vào đây]
   Auto Test On Start: true
   ```

4. **Sử dụng Context Menu:**
   ```
   Right-click NPCShoppingTester → Test Toàn Bộ Hệ Thống
   Right-click NPCShoppingTester → Test Camera Focus
   Right-click NPCShoppingTester → Test Dialogue System
   Right-click NPCShoppingTester → Test Full Interaction
   ```

**Cách 2: NPCShoppingDebugger (Có UI)**
1. **Tạo Debug GameObject:**
   ```
   GameObject → Create Empty → Đặt tên "NPCShoppingDebugger"
   ```

2. **Thêm Component:**
   ```
   Add Component → NPCShoppingDebugger
   ```

3. **Cấu hình:**
   ```
   Target NPC: [Kéo NPC vào đây]
   Target Shop: [Kéo Shop vào đây]
   ```

### Kiểm Tra Hoạt Động:

1. **Chạy Game và xem Debug UI:**
   - Debug UI sẽ hiển thị ở góc trái màn hình
   - Theo dõi trạng thái NPC và Shop realtime
   - Xem debug messages để biết NPC đang làm gì

2. **Console Logs:**
   - Bật "Hiển Thị Log" trong tất cả components
   - Theo dõi Console để xem chi tiết behavior

3. **Scene View:**
   - Bật "Hiển Thị Gizmo" trong ShopDisplayZone
   - Xem wireframe của trigger zones (màu xanh lá)

4. **Sử dụng Debug Buttons:**
   - "Kiểm Tra Hệ Thống": Kiểm tra toàn bộ setup
   - "Force NPC Tìm Shop": Bắt NPC tìm shop ngay lập tức
   - "Reset NPC Money": Reset tiền NPC về ban đầu

### Lỗi Thường Gặp và Cách Sửa:

❌ **NPC không di chuyển:**
- Kiểm tra NavMesh đã bake chưa: `Window → AI → Navigation → Bake`
- Đảm bảo NPC có NavMeshAgent và `isOnNavMesh = true`
- Kiểm tra ground có Static và Navigation Static

❌ **NPC không tìm thấy shop:**
- Tăng "Bán Kính Tìm Kiếm" trong NPCShoppingAI (thử 50)
- Kiểm tra ShopDisplayZone có BoxCollider với `isTrigger = true`
- Đảm bảo NPC có đủ tiền (xem Debug UI)

❌ **NPC không đủ tiền:**
- Tăng "Số Tiền Ban Đầu" trong NPCCurrencyManager
- Giảm "Giá Bán" trong ShopDisplayZone
- Tăng "Phần Trăm Chi Tiêu" trong NPCCurrencyManager

❌ **Text không hiển thị:**
- Kiểm tra Canvas Render Mode = World Space
- Đảm bảo TextMeshPro đã import
- Kiểm tra WorldSpaceTextDisplay được gán đúng

❌ **Trigger không hoạt động:**
- Đảm bảo ShopDisplayZone có Collider với isTrigger = true
- Kiểm tra NPC có Collider (không cần trigger)
- Xem Console log khi NPC đi qua shop zone

---

## 📝 Ghi Chú

### Tích Hợp Với Economy System:
- Hệ thống tự động tích hợp với EconomySystemManager hiện có
- NPCCurrencyManager độc lập với player currency
- Có thể mở rộng để sync với player shop profits

### Mở Rộng Tương Lai:
- Camera Focus Controller (đang phát triển)
- NPC Dialogue System (đang phát triển)  
- Multiple item preferences per NPC
- Dynamic pricing based on supply/demand

### Performance:
- NPC chỉ tìm shop mỗi 10 giây
- Text display tự động ẩn sau timeout
- NavMeshAgent tự động optimize pathfinding

---

## 🚨 Checklist Debug Nhanh

**Nếu NPC chỉ đi lang thang mà không tương tác với shop:**

1. ✅ **Kiểm tra NavMesh:** `Window → AI → Navigation → Bake`
2. ✅ **Kiểm tra tiền NPC:** Debug UI hiển thị "Có thể chi: > 0 Lea"
3. ✅ **Kiểm tra shop trigger:** ShopDisplayZone có BoxCollider isTrigger = true
4. ✅ **Kiểm tra khoảng cách:** NPC trong bán kính tìm kiếm shop
5. ✅ **Kiểm tra Console:** Có log "Tìm thấy X shop zones trong scene"
6. ✅ **Kiểm tra Debug UI:** Trạng thái NPC thay đổi từ Wandering

**Thứ tự debug:**
1. Chạy game → Mở Debug UI
2. Bấm "Kiểm Tra Hệ Thống" → Xem kết quả
3. Bấm "Force NPC Tìm Shop" → Xem NPC có phản ứng không
4. Nếu vẫn lỗi → Kiểm tra Console logs chi tiết

---

## ✅ Checklist Hoàn Thành

```
Setup Cơ Bản:
├── ShopDisplayZone: ✓
├── NPCShoppingAI: ✓
├── NPCCurrencyManager: ✓
├── WorldSpaceTextDisplay: ✓
├── NPCShoppingDebugger: ✓ (MỚI)
└── NavMesh Baked: ✓

Tích Hợp:
├── Economy System: ✓
├── 3D Models: ✓
├── UI Canvas: ✓
├── Debug Logs: ✓
└── Debug UI: ✓ (MỚI)

Hoàn Thành Mới:
├── Camera Focus: ✅
├── Dialogue System: ✅
├── Player Interaction: ✅
└── Full Integration: ✅

Có Thể Mở Rộng:
├── Multiple Dialogue Branches: ⏳
├── Animation Effects: ⏳
├── Sound Effects: ⏳
└── Advanced UI: ⏳
```
