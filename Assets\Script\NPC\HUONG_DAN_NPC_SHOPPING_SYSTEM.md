# 🛍️ Hướng Dẫn NPC Shopping System

## 📋 Tổng Quan

Hệ thống AI NPC Shopping cho phép tạo ra các NPC thông minh có thể:
- 🚶 Di chuyển tự động đến các vùng trưng bày hàng hóa
- 💰 Kiểm tra tiền và quyết định mua hàng
- 💬 Hiển thị "I want to buy..." trên đầu
- 🎯 Chờ player click để tương tác
- 📷 Trigger camera zoom và hội thoại

---

## 🚀 Thiết Lập Cơ Bản

### Bước 1: Tạo Shop Display Zone

1. **Tạo GameObject mới:**
   ```
   GameObject → Create Empty → Đặt tên "ShopZone_Wood"
   ```

2. **Thêm Component:**
   ```
   Add Component → ShopDisplayZone
   ```

3. **Cấu hình trong Inspector:**
   ```
   Item ID: "wood"
   Tên Hiển Thị: "Wood"
   Gi<PERSON>: 50
   Kích Thước Zone: (2, 2, 2)
   ```

4. **Đặt 3D Model hàng hóa:**
   - Kéo 3D model gỗ vào scene
   - Gán vào field "Item Display"

### Bước 2: Tạo NPC Shopping AI

1. **Tạo NPC GameObject:**
   ```
   GameObject → 3D Object → Capsule → Đặt tên "ShoppingNPC"
   ```

2. **Thêm các Components cần thiết:**
   ```
   Add Component → Nav Mesh Agent
   Add Component → NPCCurrencyManager  
   Add Component → NPCShoppingAI
   ```

3. **Cấu hình NPCCurrencyManager:**
   ```
   Số Tiền Ban Đầu: 1000
   Số Tiền Tối Thiểu: 100
   Phần Trăm Chi Tiêu: 0.3 (30%)
   ```

4. **Cấu hình NPCShoppingAI:**
   ```
   Tên NPC: "Shopping Bot"
   Tốc Độ Chuyển Động: 3.5
   Bán Kính Tìm Kiếm: 20
   Thời Gian Chờ Player: 15
   ```

### Bước 3: Tạo World Space Text Display

1. **Tạo Canvas cho NPC:**
   ```
   Right-click NPC → UI → Canvas
   Đặt tên: "NPCTextCanvas"
   ```

2. **Cấu hình Canvas:**
   ```
   Render Mode: World Space
   Canvas Scaler → UI Scale Mode: Scale With Screen Size
   ```

3. **Tạo Text:**
   ```
   Right-click Canvas → UI → Text - TextMeshPro
   Đặt tên: "NPCText"
   ```

4. **Thêm WorldSpaceTextDisplay:**
   ```
   Add Component → WorldSpaceTextDisplay (vào Canvas)
   ```

5. **Gán References:**
   ```
   Text Component: NPCText
   Canvas: NPCTextCanvas
   Offset Position: (0, 2.5, 0)
   ```

6. **Gán vào NPCShoppingAI:**
   ```
   Text Display: NPCTextCanvas (GameObject có WorldSpaceTextDisplay)
   ```

---

## 🎮 Cách Sử Dụng

### Trong Game:

1. **NPC tự động hoạt động:**
   - NPC sẽ đi lang thang trong scene
   - Tìm kiếm shop zones trong bán kính
   - Kiểm tra đủ tiền mua hàng

2. **Khi NPC muốn mua:**
   - Di chuyển đến shop zone
   - Đứng ở vị trí xem hàng
   - Hiển thị "I want to buy [item] (price Lea)"

3. **Player tương tác:**
   - Click vào NPC khi có text hiển thị
   - Camera sẽ zoom vào (cần implement thêm)
   - Hệ thống hội thoại xuất hiện (cần implement thêm)

---

## 🔧 Cấu Hình Nâng Cao

### NavMesh Setup:

1. **Bake NavMesh:**
   ```
   Window → AI → Navigation
   Chọn tất cả static objects
   Bake
   ```

2. **Kiểm tra NPC có NavMeshAgent:**
   ```
   Agent Type: Humanoid
   Base Offset: 0
   Speed: 3.5
   Stopping Distance: 1
   ```

### Tùy Chỉnh Hành Vi:

```csharp
// Trong NPCShoppingAI Inspector:
Thời Gian Chờ Tìm Shop: 10s    // Tần suất tìm shop
Bán Kính Tìm Kiếm: 20          // Khoảng cách tìm shop
Thời Gian Chờ Player: 15s      // Timeout chờ player click
```

### Tùy Chỉnh Tiền Tệ:

```csharp
// Trong NPCCurrencyManager Inspector:
Số Tiền Ban Đầu: 1000          // Tiền khởi tạo
Phần Trăm Chi Tiêu: 0.3        // 30% tổng tiền dư thừa
Thời Gian Cooldown: 30s        // Thời gian giữa các lần mua
```

---

## 🎨 Tùy Chỉnh UI Text

### Trong WorldSpaceTextDisplay:

```csharp
Kích Thước Text: 24
Màu Text Mặc Định: White
Thời Gian Hiển Thị: 5s
Có Hiệu Ứng Fade: true
Có Hiệu Ứng Bounce: true
```

### Tùy Chỉnh Vị Trí Text:

```csharp
Offset Position: (0, 2.5, 0)   // Cao hơn đầu NPC
Luôn Nhìn Camera: true         // Text luôn quay về camera
```

---

## 🔍 Debug và Kiểm Tra

### Kiểm Tra Hoạt Động:

1. **Console Logs:**
   - Bật "Hiển Thị Log" trong tất cả components
   - Theo dõi Console để xem NPC behavior

2. **Scene View:**
   - Bật "Hiển Thị Gizmo" trong ShopDisplayZone
   - Xem wireframe của trigger zones

3. **Inspector Debug:**
   - Xem "Trạng Thái Hiện Tại" trong NPCShoppingAI
   - Kiểm tra "Số Tiền Hiện Tại" trong NPCCurrencyManager

### Lỗi Thường Gặp:

❌ **NPC không di chuyển:**
- Kiểm tra NavMesh đã bake chưa
- Đảm bảo NPC có NavMeshAgent

❌ **Text không hiển thị:**
- Kiểm tra Canvas Render Mode = World Space
- Đảm bảo TextMeshPro đã import

❌ **NPC không tìm thấy shop:**
- Tăng "Bán Kính Tìm Kiếm"
- Kiểm tra ShopDisplayZone có Collider trigger

---

## 📝 Ghi Chú

### Tích Hợp Với Economy System:
- Hệ thống tự động tích hợp với EconomySystemManager hiện có
- NPCCurrencyManager độc lập với player currency
- Có thể mở rộng để sync với player shop profits

### Mở Rộng Tương Lai:
- Camera Focus Controller (đang phát triển)
- NPC Dialogue System (đang phát triển)  
- Multiple item preferences per NPC
- Dynamic pricing based on supply/demand

### Performance:
- NPC chỉ tìm shop mỗi 10 giây
- Text display tự động ẩn sau timeout
- NavMeshAgent tự động optimize pathfinding

---

## ✅ Checklist Hoàn Thành

```
Setup Cơ Bản:
├── ShopDisplayZone: ✓
├── NPCShoppingAI: ✓
├── NPCCurrencyManager: ✓
├── WorldSpaceTextDisplay: ✓
└── NavMesh Baked: ✓

Tích Hợp:
├── Economy System: ✓
├── 3D Models: ✓
├── UI Canvas: ✓
└── Debug Logs: ✓

Cần Phát Triển:
├── Camera Focus: ⏳
├── Dialogue System: ⏳
├── Player Interaction: ⏳
└── Transaction UI: ⏳
```
