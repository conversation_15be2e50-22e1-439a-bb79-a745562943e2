using UnityEngine;

/// <summary>
/// Component đơn giản cho vật có thể nhặt
/// Chỉ cần add vào GameObject và set tag "Pickupable"
/// </summary>
public class PickupableItem : MonoBehaviour
{
    [Header("📦 Item Info")]
    [SerializeField] private string m_ItemName = "Item";
    [SerializeField] private bool m_CanBePickedUp = true;
    
    [Header("🎨 Visual Effects (Optional)")]
    [SerializeField] private bool m_EnableFloating = false;
    [SerializeField] private float m_FloatSpeed = 1f;
    [SerializeField] private float m_FloatHeight = 0.1f;
    [SerializeField] private bool m_EnableRotation = false;
    [SerializeField] private float m_RotationSpeed = 30f;
    
    private Vector3 m_StartPosition;
    
    void Start()
    {
        m_StartPosition = transform.position;
        
        // Đảm bảo có tag đúng
        if (!gameObject.CompareTag("Pickupable"))
        {
            gameObject.tag = "Pickupable";
        }
        
        // Đ<PERSON>m bảo có Collider
        if (GetComponent<Collider>() == null)
        {
            gameObject.AddComponent<BoxCollider>();
        }
        
        // Đảm bảo có Rigidbody
        if (GetComponent<Rigidbody>() == null)
        {
            Rigidbody rb = gameObject.AddComponent<Rigidbody>();
            rb.mass = 1f;
        }
    }
    
    void Update()
    {
        if (!m_CanBePickedUp) return;
        
        // Floating effect
        if (m_EnableFloating)
        {
            float newY = m_StartPosition.y + Mathf.Sin(Time.time * m_FloatSpeed) * m_FloatHeight;
            transform.position = new Vector3(transform.position.x, newY, transform.position.z);
        }
        
        // Rotation effect
        if (m_EnableRotation)
        {
            transform.Rotate(Vector3.up, m_RotationSpeed * Time.deltaTime);
        }
    }
    
    /// <summary>
    /// Gọi khi vật được nhặt
    /// </summary>
    public void OnPickedUp()
    {
        Debug.Log($"Item {m_ItemName} được nhặt!");
        // Có thể thêm sound effect, particle effect ở đây
    }
    
    /// <summary>
    /// Gọi khi vật được thả
    /// </summary>
    public void OnDropped()
    {
        Debug.Log($"Item {m_ItemName} được thả!");
        m_StartPosition = transform.position; // Cập nhật vị trí floating mới
    }
    
    /// <summary>
    /// Thiết lập có thể nhặt hay không
    /// </summary>
    public void SetCanBePickedUp(bool canPickup)
    {
        m_CanBePickedUp = canPickup;
    }
    
    // Properties
    public string ItemName => m_ItemName;
    public bool CanBePickedUp => m_CanBePickedUp;
}
