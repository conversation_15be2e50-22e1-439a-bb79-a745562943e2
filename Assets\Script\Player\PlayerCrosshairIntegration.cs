using UnityEngine;

namespace PlayerSystem
{
    /// <summary>
    /// Tích hợp Crosshair v<PERSON><PERSON> hệ thống Player hiện có
    /// Tự động cập nhật trạng thái crosshair dựa trên tương tác
    /// </summary>
    public class PlayerCrosshairIntegration : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎯 Crosshair Integration")]
        [Ser<PERSON>ize<PERSON><PERSON>, <PERSON><PERSON><PERSON>("Crosshair Controller")]
        private CrosshairController m_CrosshairController;
        
        [SerializeField, Toolt<PERSON>("Có tự động tích hợp với Pickup System")]
        private bool m_TichHopPickupSystem = true;
        
        [SerializeField, Tooltip("Có tự động tích hợp với Economy System")]
        private bool m_TichHopEconomySystem = true;

        [Header("🔧 System References")]
        [SerializeField, Toolt<PERSON>("Pickup System")]
        private PickupSystem m_PickupSystem;
        
        [Ser<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Player Controller")]
        private PlayerController m_PlayerController;
        
        [Ser<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Player Input Handler")]
        private PlayerInputHandler m_PlayerInputHandler;

        [Header("⚙️ Settings")]
        [SerializeField, Tooltip("Có hiển thị crosshair khi bắt đầu")]
        private bool m_HienThiKhiBatDau = true;
        
        [SerializeField, Tooltip("Ẩn crosshair khi mở menu")]
        private bool m_AnKhiMoMenu = true;
        
        [SerializeField, Tooltip("Thay đổi crosshair khi aim")]
        private bool m_ThayDoiKhiAim = true;
        #endregion

        #region Private Fields
        private bool m_DaKhoiTao = false;
        private CrosshairController.CrosshairState m_TrangThaiTruoc = CrosshairController.CrosshairState.Normal;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            TimCacComponent();
        }

        private void Start()
        {
            KhoiTaoTichHop();
        }

        private void Update()
        {
            if (!m_DaKhoiTao) return;
            
            CapNhatCrosshairTheoTrangThai();
        }

        private void OnEnable()
        {
            DangKyEvents();
        }

        private void OnDisable()
        {
            HuyDangKyEvents();
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Thiết lập tích hợp thủ công
        /// </summary>
        public void ThietLapTichHop()
        {
            TimCacComponent();
            KhoiTaoTichHop();
        }

        /// <summary>
        /// Bật/tắt crosshair
        /// </summary>
        public void BatTatCrosshair(bool hienThi)
        {
            if (m_CrosshairController != null)
            {
                if (hienThi)
                {
                    m_CrosshairController.HienThiCrosshair();
                }
                else
                {
                    m_CrosshairController.AnCrosshair();
                }
            }
        }

        /// <summary>
        /// Thay đổi trạng thái crosshair thủ công
        /// </summary>
        public void ThayDoiTrangThaiCrosshair(CrosshairController.CrosshairState trangThai)
        {
            if (m_CrosshairController != null)
            {
                m_CrosshairController.ThayDoiTrangThai(trangThai);
            }
        }

        /// <summary>
        /// Reset crosshair về trạng thái mặc định
        /// </summary>
        public void ResetCrosshair()
        {
            if (m_CrosshairController != null)
            {
                m_CrosshairController.ResetVeMacDinh();
            }
        }
        #endregion

        #region Private Methods
        private void TimCacComponent()
        {
            // Tìm CrosshairController
            if (m_CrosshairController == null)
            {
                m_CrosshairController = GetComponent<CrosshairController>();
                if (m_CrosshairController == null)
                {
                    m_CrosshairController = FindObjectOfType<CrosshairController>();
                }
            }

            // Tìm PickupSystem
            if (m_PickupSystem == null && m_TichHopPickupSystem)
            {
                m_PickupSystem = GetComponent<PickupSystem>();
                if (m_PickupSystem == null)
                {
                    m_PickupSystem = FindObjectOfType<PickupSystem>();
                }
            }

            // Tìm PlayerController
            if (m_PlayerController == null)
            {
                m_PlayerController = GetComponent<PlayerController>();
                if (m_PlayerController == null)
                {
                    m_PlayerController = FindObjectOfType<PlayerController>();
                }
            }

            // Tìm PlayerInputHandler
            if (m_PlayerInputHandler == null)
            {
                m_PlayerInputHandler = GetComponent<PlayerInputHandler>();
                if (m_PlayerInputHandler == null)
                {
                    m_PlayerInputHandler = FindObjectOfType<PlayerInputHandler>();
                }
            }
        }

        private void KhoiTaoTichHop()
        {
            if (m_CrosshairController == null)
            {
                Debug.LogWarning("[PlayerCrosshairIntegration] Không tìm thấy CrosshairController!");
                return;
            }

            // Hiển thị crosshair ban đầu
            if (m_HienThiKhiBatDau)
            {
                m_CrosshairController.HienThiCrosshair();
            }

            m_DaKhoiTao = true;
            Debug.Log("[PlayerCrosshairIntegration] Đã khởi tạo tích hợp crosshair");
        }

        private void DangKyEvents()
        {
            // Đăng ký events nếu cần
            // Ví dụ: menu events, game state events, etc.
        }

        private void HuyDangKyEvents()
        {
            // Hủy đăng ký events
        }

        private void CapNhatCrosshairTheoTrangThai()
        {
            if (m_CrosshairController == null) return;

            CrosshairController.CrosshairState trangThaiMoi = XacDinhTrangThaiCrosshair();
            
            if (trangThaiMoi != m_TrangThaiTruoc)
            {
                m_CrosshairController.ThayDoiTrangThai(trangThaiMoi);
                m_TrangThaiTruoc = trangThaiMoi;
            }
        }

        private CrosshairController.CrosshairState XacDinhTrangThaiCrosshair()
        {
            // Kiểm tra Pickup System
            if (m_TichHopPickupSystem && m_PickupSystem != null)
            {
                // Kiểm tra nếu có vật phẩm có thể pickup
                if (CoVatPhamCoThePickup())
                {
                    return CrosshairController.CrosshairState.CanInteract;
                }
                
                // Kiểm tra nếu đang mang vật phẩm
                if (DangMangVatPham())
                {
                    return CrosshairController.CrosshairState.CanInteract;
                }
            }

            // Kiểm tra Economy System
            if (m_TichHopEconomySystem)
            {
                if (CoTheGiaoDich())
                {
                    return CrosshairController.CrosshairState.CanInteract;
                }
            }

            // Kiểm tra các tương tác khác
            if (CoTuongTacKhac())
            {
                return CrosshairController.CrosshairState.CanInteract;
            }

            return CrosshairController.CrosshairState.Normal;
        }

        private bool CoVatPhamCoThePickup()
        {
            if (m_PickupSystem == null) return false;
            
            // Sử dụng reflection hoặc public property để kiểm tra
            // Hoặc tạo interface/event để communication
            try
            {
                // Giả sử PickupSystem có property CurrentDetectedItem
                var currentItemField = m_PickupSystem.GetType().GetField("m_CurrentDetectedItem", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                if (currentItemField != null)
                {
                    var currentItem = currentItemField.GetValue(m_PickupSystem);
                    return currentItem != null;
                }
            }
            catch (System.Exception)
            {
                // Fallback: sử dụng cách khác để detect
            }
            
            return false;
        }

        private bool DangMangVatPham()
        {
            if (m_PickupSystem == null) return false;
            
            try
            {
                // Kiểm tra property IsCarryingItems
                var isCarryingProperty = m_PickupSystem.GetType().GetProperty("IsCarryingItems");
                if (isCarryingProperty != null)
                {
                    return (bool)isCarryingProperty.GetValue(m_PickupSystem);
                }
            }
            catch (System.Exception)
            {
                // Fallback
            }
            
            return false;
        }

        private bool CoTheGiaoDich()
        {
            // Kiểm tra nếu player đang nhìn vào trading machine
            // Có thể sử dụng raycast hoặc trigger detection
            
            Camera playerCamera = Camera.main;
            if (playerCamera == null) return false;
            
            RaycastHit hit;
            if (Physics.Raycast(playerCamera.transform.position, playerCamera.transform.forward, out hit, 5f))
            {
                // Kiểm tra nếu hit object có component liên quan đến economy
                if (hit.collider.GetComponent<EconomySystem.TradingMachine>() != null ||
                    hit.collider.GetComponent<EconomySystem.AdvancedTradingMachine>() != null)
                {
                    return true;
                }
            }
            
            return false;
        }

        private bool CoTuongTacKhac()
        {
            // Kiểm tra các loại tương tác khác
            // Ví dụ: doors, buttons, NPCs, etc.
            
            Camera playerCamera = Camera.main;
            if (playerCamera == null) return false;
            
            RaycastHit hit;
            if (Physics.Raycast(playerCamera.transform.position, playerCamera.transform.forward, out hit, 3f))
            {
                // Kiểm tra tag hoặc component
                if (hit.collider.CompareTag("Interactable") ||
                    hit.collider.GetComponent<IInteractable>() != null)
                {
                    return true;
                }
            }
            
            return false;
        }

        /// <summary>
        /// Xử lý khi menu được mở/đóng
        /// </summary>
        public void OnMenuStateChanged(bool menuOpen)
        {
            if (!m_AnKhiMoMenu) return;
            
            if (m_CrosshairController != null)
            {
                if (menuOpen)
                {
                    m_CrosshairController.AnCrosshair();
                }
                else
                {
                    m_CrosshairController.HienThiCrosshair();
                }
            }
        }

        /// <summary>
        /// Xử lý khi player aim
        /// </summary>
        public void OnAimStateChanged(bool isAiming)
        {
            if (!m_ThayDoiKhiAim) return;
            
            if (m_CrosshairController != null)
            {
                if (isAiming)
                {
                    // Có thể thay đổi kiểu crosshair khi aim
                    m_CrosshairController.ThayDoiKieu(CrosshairController.CrosshairType.Dot);
                    m_CrosshairController.ThayDoiKichThuoc(0.5f);
                }
                else
                {
                    // Reset về crosshair bình thường
                    m_CrosshairController.ThayDoiKieu(CrosshairController.CrosshairType.Cross);
                    m_CrosshairController.ThayDoiKichThuoc(1f);
                }
            }
        }
        #endregion

        #region Debug
        private void OnDrawGizmosSelected()
        {
            // Vẽ raycast để debug interaction detection
            Camera playerCamera = Camera.main;
            if (playerCamera != null)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawRay(playerCamera.transform.position, playerCamera.transform.forward * 5f);
            }
        }
        #endregion
    }

    /// <summary>
    /// Interface cho các object có thể tương tác
    /// </summary>
    public interface IInteractable
    {
        bool CanInteract { get; }
        void Interact();
        string GetInteractionPrompt();
    }
}
