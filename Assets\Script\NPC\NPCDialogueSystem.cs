using UnityEngine;
using System.Collections.Generic;
using System.Collections;

namespace NPCSystem
{
    /// <summary>
    /// <PERSON><PERSON> thống hội thoại cho NPC Shopping
    /// Hiển thị các lựa chọn hội thoại bằng tiếng Anh
    /// </summary>
    public class NPCDialogueSystem : MonoBehaviour
    {
        #region Enums
        public enum DialogueState
        {
            Closed,
            Opening,
            WaitingForChoice,
            Processing,
            Closing
        }
        #endregion

        #region Serialized Fields
        [Header("💬 Dialogue Settings")]
        [SerializeField, <PERSON>lt<PERSON>("UI Canvas cho dialogue")]
        private Canvas m_DialogueCanvas;
        
        [SerializeField, Tooltip("Panel chứa dialogue")]
        private GameObject m_DialoguePanel;
        
        [SerializeField, Toolt<PERSON>("Text hiển thị tên NPC")]
        private TMPro.TextMeshProUGUI m_NPCNameText;
        
        [SerializeField, Tooltip("Text hiển thị nội dung hội thoại")]
        private TMPro.TextMeshProUGUI m_DialogueText;
        
        [SerializeField, <PERSON><PERSON><PERSON>("Parent chứa các buttons lựa chọn")]
        private Transform m_ChoiceButtonsParent;
        
        [SerializeField, Tooltip("Prefab button cho lựa chọn")]
        private GameObject m_ChoiceButtonPrefab;
        
        [Header("🎨 Visual Effects")]
        [SerializeField, Tooltip("Thời gian fade in/out")]
        private float m_FadeTime = 0.3f;
        
        [SerializeField, Tooltip("Có hiệu ứng typewriter")]
        private bool m_TypewriterEffect = true;
        
        [SerializeField, Tooltip("Tốc độ typewriter (ký tự/giây)")]
        private float m_TypewriterSpeed = 30f;
        
        [Header("🔧 Debug")]
        [SerializeField, Tooltip("Hiển thị log debug")]
        private bool m_HienThiLog = true;
        #endregion

        #region Private Fields
        private DialogueState m_CurrentState = DialogueState.Closed;
        private NPCShoppingAI m_CurrentNPC;
        private ShopDisplayZone m_CurrentShop;
        private List<GameObject> m_ActiveChoiceButtons = new List<GameObject>();
        private Coroutine m_TypewriterCoroutine;
        private CanvasGroup m_DialogueCanvasGroup;
        #endregion

        #region Properties
        public DialogueState CurrentState => m_CurrentState;
        public bool IsOpen => m_CurrentState != DialogueState.Closed;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            KhoiTaoComponents();
        }

        private void Start()
        {
            AnDialogue();
            Log("NPCDialogueSystem đã khởi tạo");
        }

        private void Update()
        {
            // Có thể thêm input handling ở đây (ESC để đóng dialogue, etc.)
            if (IsOpen && Input.GetKeyDown(KeyCode.Escape))
            {
                DongDialogue();
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Mở dialogue với NPC
        /// </summary>
        public void MoDialogue(NPCShoppingAI npc, ShopDisplayZone shop)
        {
            if (IsOpen)
            {
                Log("Dialogue đã mở, bỏ qua request");
                return;
            }

            if (npc == null)
            {
                LogLoi("NPC null!");
                return;
            }

            Log($"Mở dialogue với NPC: {npc.TenNPC}");
            
            m_CurrentNPC = npc;
            m_CurrentShop = shop;
            m_CurrentState = DialogueState.Opening;
            
            StartCoroutine(MoDialogueCoroutine());
        }

        /// <summary>
        /// Đóng dialogue
        /// </summary>
        public void DongDialogue()
        {
            if (!IsOpen)
            {
                Log("Dialogue đã đóng");
                return;
            }

            Log("Đóng dialogue");
            m_CurrentState = DialogueState.Closing;
            StartCoroutine(DongDialogueCoroutine());
        }

        /// <summary>
        /// Xử lý lựa chọn của player
        /// </summary>
        public void OnPlayerChoice(int choiceIndex)
        {
            if (m_CurrentState != DialogueState.WaitingForChoice)
            {
                Log("Không thể xử lý lựa chọn lúc này");
                return;
            }

            Log($"Player chọn: {choiceIndex}");
            m_CurrentState = DialogueState.Processing;
            
            StartCoroutine(XuLyLuaChon(choiceIndex));
        }
        #endregion

        #region Private Methods
        private void KhoiTaoComponents()
        {
            // Tự động tìm components nếu chưa gán
            if (m_DialogueCanvas == null)
            {
                m_DialogueCanvas = GetComponentInChildren<Canvas>();
            }
            
            if (m_DialogueCanvas != null)
            {
                m_DialogueCanvasGroup = m_DialogueCanvas.GetComponent<CanvasGroup>();
                if (m_DialogueCanvasGroup == null)
                {
                    m_DialogueCanvasGroup = m_DialogueCanvas.gameObject.AddComponent<CanvasGroup>();
                }
            }
        }

        private void AnDialogue()
        {
            if (m_DialogueCanvas != null)
            {
                m_DialogueCanvas.gameObject.SetActive(false);
            }
            
            if (m_DialoguePanel != null)
            {
                m_DialoguePanel.SetActive(false);
            }
            
            m_CurrentState = DialogueState.Closed;
        }

        private IEnumerator MoDialogueCoroutine()
        {
            // Hiển thị canvas
            if (m_DialogueCanvas != null)
            {
                m_DialogueCanvas.gameObject.SetActive(true);
            }
            
            if (m_DialoguePanel != null)
            {
                m_DialoguePanel.SetActive(true);
            }
            
            // Fade in effect
            if (m_DialogueCanvasGroup != null)
            {
                yield return StartCoroutine(FadeCanvasGroup(m_DialogueCanvasGroup, 0f, 1f, m_FadeTime));
            }
            
            // Thiết lập nội dung dialogue
            ThietLapNoiDungDialogue();
            
            // Tạo các buttons lựa chọn
            TaoButtonsLuaChon();
            
            m_CurrentState = DialogueState.WaitingForChoice;
            Log("Dialogue đã mở, chờ player lựa chọn");
        }

        private IEnumerator DongDialogueCoroutine()
        {
            // Fade out effect
            if (m_DialogueCanvasGroup != null)
            {
                yield return StartCoroutine(FadeCanvasGroup(m_DialogueCanvasGroup, 1f, 0f, m_FadeTime));
            }
            
            // Ẩn dialogue
            AnDialogue();
            
            // Cleanup
            m_CurrentNPC = null;
            m_CurrentShop = null;
            XoaButtonsLuaChon();
            
            Log("Dialogue đã đóng");
        }

        private void ThietLapNoiDungDialogue()
        {
            if (m_CurrentNPC == null) return;
            
            // Thiết lập tên NPC
            if (m_NPCNameText != null)
            {
                m_NPCNameText.text = m_CurrentNPC.TenNPC;
            }
            
            // Thiết lập nội dung hội thoại
            string dialogueContent = GetDialogueContent();
            
            if (m_DialogueText != null)
            {
                if (m_TypewriterEffect)
                {
                    if (m_TypewriterCoroutine != null)
                    {
                        StopCoroutine(m_TypewriterCoroutine);
                    }
                    m_TypewriterCoroutine = StartCoroutine(TypewriterEffect(dialogueContent));
                }
                else
                {
                    m_DialogueText.text = dialogueContent;
                }
            }
        }

        private string GetDialogueContent()
        {
            if (m_CurrentShop == null)
            {
                return "Hello! How can I help you today?";
            }
            
            return $"I'm interested in buying {m_CurrentShop.TenHienThi} for {m_CurrentShop.GiaBan} Lea. Can you help me with this purchase?";
        }

        private void TaoButtonsLuaChon()
        {
            if (m_ChoiceButtonsParent == null || m_ChoiceButtonPrefab == null) return;
            
            // Xóa buttons cũ
            XoaButtonsLuaChon();
            
            // Tạo buttons mới
            string[] choices = GetDialogueChoices();
            
            for (int i = 0; i < choices.Length; i++)
            {
                GameObject buttonObj = Instantiate(m_ChoiceButtonPrefab, m_ChoiceButtonsParent);
                
                // Thiết lập text
                var buttonText = buttonObj.GetComponentInChildren<TMPro.TextMeshProUGUI>();
                if (buttonText != null)
                {
                    buttonText.text = choices[i];
                }
                
                // Thiết lập button event
                var button = buttonObj.GetComponent<UnityEngine.UI.Button>();
                if (button != null)
                {
                    int choiceIndex = i; // Capture for closure
                    button.onClick.AddListener(() => OnPlayerChoice(choiceIndex));
                }
                
                m_ActiveChoiceButtons.Add(buttonObj);
            }
        }

        private string[] GetDialogueChoices()
        {
            return new string[]
            {
                "1. How can I help you?",
                "2. Sure, I can sell that to you.",
                "3. Sorry, that item is not available.",
                "4. Let me check the price for you.",
                "5. Have a nice day!"
            };
        }

        private void XoaButtonsLuaChon()
        {
            foreach (var button in m_ActiveChoiceButtons)
            {
                if (button != null)
                {
                    Destroy(button);
                }
            }
            m_ActiveChoiceButtons.Clear();
        }

        private IEnumerator XuLyLuaChon(int choiceIndex)
        {
            Log($"Xử lý lựa chọn {choiceIndex}");
            
            // Xử lý logic dựa trên lựa chọn
            switch (choiceIndex)
            {
                case 0: // "How can I help you?"
                    yield return StartCoroutine(XuLyLuaChon_HowCanIHelp());
                    break;
                    
                case 1: // "Sure, I can sell that to you."
                    yield return StartCoroutine(XuLyLuaChon_SellItem());
                    break;
                    
                case 2: // "Sorry, that item is not available."
                    yield return StartCoroutine(XuLyLuaChon_NotAvailable());
                    break;
                    
                case 3: // "Let me check the price for you."
                    yield return StartCoroutine(XuLyLuaChon_CheckPrice());
                    break;
                    
                case 4: // "Have a nice day!"
                    yield return StartCoroutine(XuLyLuaChon_Goodbye());
                    break;
            }
        }

        private IEnumerator XuLyLuaChon_HowCanIHelp()
        {
            // NPC response
            string response = "Thank you for asking! I would like to purchase this item if possible.";
            yield return StartCoroutine(HienThiResponse(response));
            
            // Quay lại chờ lựa chọn
            m_CurrentState = DialogueState.WaitingForChoice;
        }

        private IEnumerator XuLyLuaChon_SellItem()
        {
            if (m_CurrentNPC != null && m_CurrentShop != null)
            {
                // Thực hiện giao dịch
                bool thanhCong = m_CurrentNPC.ThucHienGiaoDich();
                
                string response = thanhCong 
                    ? "Thank you so much! Here's the payment." 
                    : "Oh no, I don't have enough money. Sorry!";
                    
                yield return StartCoroutine(HienThiResponse(response));
                
                // Đóng dialogue sau giao dịch
                yield return new WaitForSeconds(2f);
                DongDialogue();
            }
        }

        private IEnumerator XuLyLuaChon_NotAvailable()
        {
            string response = "Oh, that's unfortunate. Maybe next time!";
            yield return StartCoroutine(HienThiResponse(response));
            
            yield return new WaitForSeconds(2f);
            DongDialogue();
        }

        private IEnumerator XuLyLuaChon_CheckPrice()
        {
            if (m_CurrentShop != null)
            {
                string response = $"The price is {m_CurrentShop.GiaBan} Lea. Is that acceptable?";
                yield return StartCoroutine(HienThiResponse(response));
            }
            
            m_CurrentState = DialogueState.WaitingForChoice;
        }

        private IEnumerator XuLyLuaChon_Goodbye()
        {
            string response = "Thank you! Have a wonderful day!";
            yield return StartCoroutine(HienThiResponse(response));
            
            yield return new WaitForSeconds(2f);
            DongDialogue();
        }

        private IEnumerator HienThiResponse(string response)
        {
            if (m_DialogueText != null)
            {
                if (m_TypewriterEffect)
                {
                    if (m_TypewriterCoroutine != null)
                    {
                        StopCoroutine(m_TypewriterCoroutine);
                    }
                    m_TypewriterCoroutine = StartCoroutine(TypewriterEffect(response));
                    
                    // Chờ typewriter hoàn thành
                    yield return m_TypewriterCoroutine;
                }
                else
                {
                    m_DialogueText.text = response;
                }
            }
            
            yield return new WaitForSeconds(1f);
        }

        private IEnumerator TypewriterEffect(string text)
        {
            if (m_DialogueText == null) yield break;
            
            m_DialogueText.text = "";
            
            for (int i = 0; i <= text.Length; i++)
            {
                m_DialogueText.text = text.Substring(0, i);
                yield return new WaitForSeconds(1f / m_TypewriterSpeed);
            }
        }

        private IEnumerator FadeCanvasGroup(CanvasGroup canvasGroup, float startAlpha, float endAlpha, float duration)
        {
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;
                canvasGroup.alpha = Mathf.Lerp(startAlpha, endAlpha, t);
                yield return null;
            }
            
            canvasGroup.alpha = endAlpha;
        }

        private void Log(string message)
        {
            if (m_HienThiLog)
            {
                Debug.Log($"[NPCDialogue] {message}");
            }
        }

        private void LogLoi(string message)
        {
            Debug.LogError($"[NPCDialogue] {message}");
        }
        #endregion

        #region Editor Methods
        private void OnValidate()
        {
            if (m_FadeTime < 0) m_FadeTime = 0;
            if (m_TypewriterSpeed < 1) m_TypewriterSpeed = 1;
        }
        #endregion
    }
}
