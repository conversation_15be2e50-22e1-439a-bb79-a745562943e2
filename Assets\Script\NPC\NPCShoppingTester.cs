using UnityEngine;

namespace NPCSystem
{
    /// <summary>
    /// Script test đơn giản cho NPC Shopping System
    /// Chỉ sử dụng Console logs và Context Menu
    /// </summary>
    public class NPCShoppingTester : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎯 Test Targets")]
        [Serial<PERSON>Field, <PERSON><PERSON><PERSON>("NPC cần test")]
        private NPCShoppingAI m_TargetNPC;
        
        [SerializeField, Toolt<PERSON>("Shop zone cần test")]
        private ShopDisplayZone m_TargetShop;
        
        [Header("🔧 Test Settings")]
        [SerializeField, Tooltip("Tự động test khi start")]
        private bool m_AutoTestOnStart = true;
        
        [SerializeField, Toolt<PERSON>("Hiển thị thông tin liên tục")]
        private bool m_ContinuousInfo = true;
        
        [SerializeField, Tooltip("Thời gian giữa các lần hiển thị info")]
        private float m_InfoInterval = 5f;
        #endregion

        #region Private Fields
        private float m_LastInfoTime = 0f;
        #endregion

        #region Unity Lifecycle
        private void Start()
        {
            // Tự động tìm NPC và Shop nếu chưa gán
            if (m_TargetNPC == null)
            {
                m_TargetNPC = FindObjectOfType<NPCShoppingAI>();
            }
            
            if (m_TargetShop == null)
            {
                m_TargetShop = FindObjectOfType<ShopDisplayZone>();
            }
            
            if (m_AutoTestOnStart)
            {
                Invoke(nameof(TestSystem), 1f); // Test sau 1 giây
            }
            
            Debug.Log("🧪 NPCShoppingTester đã khởi tạo. Sử dụng Context Menu để test!");
        }

        private void Update()
        {
            if (m_ContinuousInfo && Time.time - m_LastInfoTime >= m_InfoInterval)
            {
                ShowCurrentInfo();
                m_LastInfoTime = Time.time;
            }
        }
        #endregion

        #region Context Menu Methods
        [ContextMenu("🧪 Test Toàn Bộ Hệ Thống")]
        public void TestSystem()
        {
            Debug.Log("=== 🧪 BẮT ĐẦU TEST HỆ THỐNG ===");
            
            TestNPCSetup();
            TestShopSetup();
            TestNavMesh();
            TestMoney();
            TestDistance();
            
            Debug.Log("=== ✅ KẾT THÚC TEST ===");
        }

        [ContextMenu("🤖 Test NPC Setup")]
        public void TestNPCSetup()
        {
            Debug.Log("--- Test NPC Setup ---");
            
            if (m_TargetNPC == null)
            {
                Debug.LogError("❌ Không tìm thấy NPC!");
                return;
            }
            
            Debug.Log($"✅ NPC: {m_TargetNPC.name}");
            Debug.Log($"   Trạng thái: {m_TargetNPC.TrangThaiHienTai}");
            
            // Test components
            var currency = m_TargetNPC.GetComponent<NPCCurrencyManager>();
            var agent = m_TargetNPC.GetComponent<UnityEngine.AI.NavMeshAgent>();
            
            Debug.Log($"   NPCCurrencyManager: {(currency != null ? "✅" : "❌")}");
            Debug.Log($"   NavMeshAgent: {(agent != null ? "✅" : "❌")}");
            
            if (agent != null)
            {
                Debug.Log($"   Agent enabled: {agent.enabled}");
                Debug.Log($"   On NavMesh: {agent.isOnNavMesh}");
            }
        }

        [ContextMenu("🏪 Test Shop Setup")]
        public void TestShopSetup()
        {
            Debug.Log("--- Test Shop Setup ---");
            
            if (m_TargetShop == null)
            {
                Debug.LogError("❌ Không tìm thấy Shop!");
                return;
            }
            
            Debug.Log($"✅ Shop: {m_TargetShop.name}");
            Debug.Log($"   Tên hiển thị: {m_TargetShop.TenHienThi}");
            Debug.Log($"   Giá: {m_TargetShop.GiaBan} Lea");
            
            // Test collider
            var collider = m_TargetShop.GetComponent<Collider>();
            Debug.Log($"   Collider: {(collider != null ? "✅" : "❌")}");
            if (collider != null)
            {
                Debug.Log($"   Is Trigger: {(collider.isTrigger ? "✅" : "❌")}");
            }
            
            // Test tất cả shops trong scene
            var allShops = FindObjectsOfType<ShopDisplayZone>();
            Debug.Log($"   Tổng số shops trong scene: {allShops.Length}");
        }

        [ContextMenu("🗺️ Test NavMesh")]
        public void TestNavMesh()
        {
            Debug.Log("--- Test NavMesh ---");
            
            // Kiểm tra NavMesh có được bake chưa
            var navMeshData = UnityEngine.AI.NavMesh.CalculateTriangulation();
            Debug.Log($"NavMesh triangles: {navMeshData.indices.Length / 3}");
            
            if (navMeshData.indices.Length == 0)
            {
                Debug.LogError("❌ NavMesh chưa được bake! Vào Window → AI → Navigation → Bake");
                return;
            }
            
            Debug.Log("✅ NavMesh đã được bake");
            
            // Test vị trí NPC trên NavMesh
            if (m_TargetNPC != null)
            {
                UnityEngine.AI.NavMeshHit hit;
                bool onNavMesh = UnityEngine.AI.NavMesh.SamplePosition(m_TargetNPC.transform.position, out hit, 1f, UnityEngine.AI.NavMesh.AllAreas);
                Debug.Log($"NPC trên NavMesh: {(onNavMesh ? "✅" : "❌")}");
            }
        }

        [ContextMenu("💰 Test Money System")]
        public void TestMoney()
        {
            Debug.Log("--- Test Money System ---");
            
            if (m_TargetNPC == null) return;
            
            var currency = m_TargetNPC.GetComponent<NPCCurrencyManager>();
            if (currency == null)
            {
                Debug.LogError("❌ NPC không có NPCCurrencyManager!");
                return;
            }
            
            Debug.Log($"✅ Tiền hiện tại: {currency.SoTienHienTai} Lea");
            Debug.Log($"   Tiền tối thiểu: {currency.SoTienToiThieu} Lea");
            Debug.Log($"   Có thể chi: {currency.GetSoTienCoTheChi()} Lea");
            Debug.Log($"   Có thể mua hàng: {currency.CoTheMuaHang}");
            
            // Test với shop
            if (m_TargetShop != null)
            {
                bool canBuy = currency.CoTheMua(m_TargetShop.GiaBan);
                Debug.Log($"   Có thể mua {m_TargetShop.TenHienThi}: {(canBuy ? "✅" : "❌")}");
            }
        }

        [ContextMenu("📏 Test Distance")]
        public void TestDistance()
        {
            Debug.Log("--- Test Distance ---");
            
            if (m_TargetNPC == null || m_TargetShop == null) return;
            
            float distance = Vector3.Distance(m_TargetNPC.transform.position, m_TargetShop.transform.position);
            Debug.Log($"Khoảng cách NPC-Shop: {distance:F1}m");
            
            // Lấy bán kính tìm kiếm từ NPC
            var npcScript = m_TargetNPC;
            // Không thể access private field, nên dùng giá trị mặc định
            float searchRadius = 20f; // Giá trị mặc định
            
            Debug.Log($"Bán kính tìm kiếm: {searchRadius}m");
            Debug.Log($"Trong phạm vi: {(distance <= searchRadius ? "✅" : "❌")}");
        }

        [ContextMenu("🔄 Force NPC Tìm Shop")]
        public void ForceNPCFindShop()
        {
            if (m_TargetNPC == null) return;
            
            Debug.Log("🔄 Force NPC tìm shop...");
            
            // Sử dụng reflection để gọi method private
            var method = typeof(NPCShoppingAI).GetMethod("TimShopZoneGanNhat", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (method != null)
            {
                method.Invoke(m_TargetNPC, null);
                Debug.Log("✅ Đã force NPC tìm shop");
            }
            else
            {
                Debug.LogError("❌ Không thể tìm thấy method TimShopZoneGanNhat");
            }
        }

        [ContextMenu("💸 Reset NPC Money")]
        public void ResetNPCMoney()
        {
            if (m_TargetNPC == null) return;
            
            var currency = m_TargetNPC.GetComponent<NPCCurrencyManager>();
            if (currency != null)
            {
                currency.ResetTien();
                Debug.Log("💸 Đã reset tiền NPC");
            }
        }

        [ContextMenu("📊 Show Current Info")]
        public void ShowCurrentInfo()
        {
            if (m_TargetNPC == null) return;

            Debug.Log($"📊 [INFO] NPC: {m_TargetNPC.TrangThaiHienTai} | Shop: {(m_TargetNPC.CurrentShopZone?.TenHienThi ?? "None")}");
        }

        [ContextMenu("📷 Test Camera Focus")]
        public void TestCameraFocus()
        {
            Debug.Log("--- Test Camera Focus ---");

            var cameraFocus = FindObjectOfType<CameraFocusController>();
            if (cameraFocus == null)
            {
                Debug.LogError("❌ Không tìm thấy CameraFocusController!");
                return;
            }

            Debug.Log("✅ CameraFocusController found");

            if (m_TargetNPC != null)
            {
                Transform itemTransform = m_TargetShop?.ItemDisplay?.transform;
                cameraFocus.FocusOnNPCAndItem(m_TargetNPC.transform, itemTransform);
                Debug.Log("🎯 Camera focus triggered");
            }
        }

        [ContextMenu("💬 Test Dialogue System")]
        public void TestDialogueSystem()
        {
            Debug.Log("--- Test Dialogue System ---");

            var dialogueSystem = FindObjectOfType<NPCDialogueSystem>();
            if (dialogueSystem == null)
            {
                Debug.LogError("❌ Không tìm thấy NPCDialogueSystem!");
                return;
            }

            Debug.Log("✅ NPCDialogueSystem found");

            if (m_TargetNPC != null)
            {
                dialogueSystem.MoDialogue(m_TargetNPC, m_TargetShop);
                Debug.Log("💬 Dialogue opened");
            }
        }

        [ContextMenu("🎭 Test Full Interaction")]
        public void TestFullInteraction()
        {
            Debug.Log("--- Test Full Interaction ---");

            if (m_TargetNPC == null)
            {
                Debug.LogError("❌ Không có target NPC!");
                return;
            }

            // Simulate player click
            m_TargetNPC.OnPlayerClick();
            Debug.Log("🖱️ Simulated player click on NPC");
        }

        [ContextMenu("🔄 Return Camera")]
        public void ReturnCamera()
        {
            var cameraFocus = FindObjectOfType<CameraFocusController>();
            if (cameraFocus != null)
            {
                cameraFocus.ReturnToNormalView();
                Debug.Log("📷 Camera returned to normal");
            }
        }

        [ContextMenu("❌ Close Dialogue")]
        public void CloseDialogue()
        {
            var dialogueSystem = FindObjectOfType<NPCDialogueSystem>();
            if (dialogueSystem != null)
            {
                dialogueSystem.DongDialogue();
                Debug.Log("💬 Dialogue closed");
            }
        }
        #endregion

        #region Gizmos
        private void OnDrawGizmos()
        {
            if (m_TargetNPC != null && m_TargetShop != null)
            {
                // Vẽ line từ NPC đến Shop
                Gizmos.color = Color.yellow;
                Gizmos.DrawLine(m_TargetNPC.transform.position, m_TargetShop.transform.position);
                
                // Vẽ sphere bán kính tìm kiếm
                Gizmos.color = Color.cyan;
                Gizmos.DrawWireSphere(m_TargetNPC.transform.position, 20f); // Bán kính mặc định
            }
        }
        #endregion
    }
}
