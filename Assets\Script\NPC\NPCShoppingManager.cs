using UnityEngine;
using System.Collections.Generic;

namespace NPCSystem
{
    /// <summary>
    /// Manager tổng thể cho NPC Shopping System
    /// Quản lý tất cả NPCs, shops, camera focus và dialogue
    /// </summary>
    public class NPCShoppingManager : MonoBehaviour
    {
        #region Singleton
        public static NPCShoppingManager Instance { get; private set; }
        #endregion

        #region Serialized Fields
        [Header("🎮 System References")]
        [SerializeField, <PERSON>lt<PERSON>("Camera Focus Controller")]
        private CameraFocusController m_CameraFocus;
        
        [SerializeField, Toolt<PERSON>("Dialogue System")]
        private NPCDialogueSystem m_DialogueSystem;
        
        [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>lt<PERSON>("Tự động tìm components")]
        private bool m_AutoFindComponents = true;
        
        [Header("🔧 System Settings")]
        [SerializeField, Toolt<PERSON>("Có cho phép multiple NPCs tương tác cùng lúc")]
        private bool m_AllowMultipleInteractions = false;
        
        [SerializeField, <PERSON><PERSON><PERSON>("Hiển thị log debug")]
        private bool m_HienThiLog = true;
        
        [Header("📊 Statistics")]
        [Serial<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Hiển thị thống kê trong Inspector")]
        private bool m_ShowStatistics = true;
        #endregion

        #region Private Fields
        private List<NPCShoppingAI> m_AllNPCs = new List<NPCShoppingAI>();
        private List<ShopDisplayZone> m_AllShops = new List<ShopDisplayZone>();
        private NPCShoppingAI m_CurrentInteractingNPC;
        private bool m_SystemInitialized = false;
        
        // Statistics
        private int m_TotalTransactions = 0;
        private int m_SuccessfulTransactions = 0;
        private float m_TotalRevenue = 0f;
        #endregion

        #region Properties
        public bool SystemInitialized => m_SystemInitialized;
        public NPCShoppingAI CurrentInteractingNPC => m_CurrentInteractingNPC;
        public int TotalNPCs => m_AllNPCs.Count;
        public int TotalShops => m_AllShops.Count;
        public int TotalTransactions => m_TotalTransactions;
        public int SuccessfulTransactions => m_SuccessfulTransactions;
        public float SuccessRate => m_TotalTransactions > 0 ? (float)m_SuccessfulTransactions / m_TotalTransactions : 0f;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            // Singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                KhoiTaoSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            if (m_AutoFindComponents)
            {
                TimTatCaComponents();
            }
            
            DangKyEvents();
            m_SystemInitialized = true;
            
            Log($"NPCShoppingManager khởi tạo: {m_AllNPCs.Count} NPCs, {m_AllShops.Count} Shops");
        }

        private void Update()
        {
            // Có thể thêm logic update ở đây nếu cần
        }

        private void OnDestroy()
        {
            if (Instance == this)
            {
                Instance = null;
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Bắt đầu tương tác với NPC
        /// </summary>
        public bool StartInteraction(NPCShoppingAI npc, ShopDisplayZone shop)
        {
            if (!m_SystemInitialized)
            {
                LogLoi("System chưa khởi tạo!");
                return false;
            }

            if (m_CurrentInteractingNPC != null && !m_AllowMultipleInteractions)
            {
                Log("Đã có NPC đang tương tác");
                return false;
            }

            if (npc == null)
            {
                LogLoi("NPC null!");
                return false;
            }

            Log($"Bắt đầu tương tác với NPC: {npc.TenNPC}");
            m_CurrentInteractingNPC = npc;

            // Trigger camera focus
            if (m_CameraFocus != null)
            {
                Transform itemTransform = shop?.ItemDisplay?.transform;
                m_CameraFocus.FocusOnNPCAndItem(npc.transform, itemTransform);
            }

            // Trigger dialogue
            if (m_DialogueSystem != null)
            {
                m_DialogueSystem.MoDialogue(npc, shop);
            }

            return true;
        }

        /// <summary>
        /// Kết thúc tương tác
        /// </summary>
        public void EndInteraction()
        {
            if (m_CurrentInteractingNPC == null)
            {
                Log("Không có tương tác nào đang diễn ra");
                return;
            }

            Log($"Kết thúc tương tác với NPC: {m_CurrentInteractingNPC.TenNPC}");

            // Return camera
            if (m_CameraFocus != null)
            {
                m_CameraFocus.ReturnToNormalView();
            }

            // Close dialogue
            if (m_DialogueSystem != null)
            {
                m_DialogueSystem.DongDialogue();
            }

            m_CurrentInteractingNPC = null;
        }

        /// <summary>
        /// Ghi nhận giao dịch
        /// </summary>
        public void RecordTransaction(bool successful, float amount = 0f)
        {
            m_TotalTransactions++;
            
            if (successful)
            {
                m_SuccessfulTransactions++;
                m_TotalRevenue += amount;
                Log($"Giao dịch thành công: +{amount} Lea");
            }
            else
            {
                Log("Giao dịch thất bại");
            }
        }

        /// <summary>
        /// Đăng ký NPC vào system
        /// </summary>
        public void RegisterNPC(NPCShoppingAI npc)
        {
            if (npc != null && !m_AllNPCs.Contains(npc))
            {
                m_AllNPCs.Add(npc);
                Log($"Đăng ký NPC: {npc.TenNPC}");
            }
        }

        /// <summary>
        /// Hủy đăng ký NPC
        /// </summary>
        public void UnregisterNPC(NPCShoppingAI npc)
        {
            if (npc != null && m_AllNPCs.Contains(npc))
            {
                m_AllNPCs.Remove(npc);
                Log($"Hủy đăng ký NPC: {npc.TenNPC}");
            }
        }

        /// <summary>
        /// Đăng ký Shop vào system
        /// </summary>
        public void RegisterShop(ShopDisplayZone shop)
        {
            if (shop != null && !m_AllShops.Contains(shop))
            {
                m_AllShops.Add(shop);
                Log($"Đăng ký Shop: {shop.TenHienThi}");
            }
        }

        /// <summary>
        /// Hủy đăng ký Shop
        /// </summary>
        public void UnregisterShop(ShopDisplayZone shop)
        {
            if (shop != null && m_AllShops.Contains(shop))
            {
                m_AllShops.Remove(shop);
                Log($"Hủy đăng ký Shop: {shop.TenHienThi}");
            }
        }

        /// <summary>
        /// Lấy thống kê hệ thống
        /// </summary>
        public string GetSystemStatistics()
        {
            return $"NPCs: {m_AllNPCs.Count} | Shops: {m_AllShops.Count} | " +
                   $"Transactions: {m_SuccessfulTransactions}/{m_TotalTransactions} ({SuccessRate:P1}) | " +
                   $"Revenue: {m_TotalRevenue:F0} Lea";
        }

        /// <summary>
        /// Reset thống kê
        /// </summary>
        [ContextMenu("Reset Statistics")]
        public void ResetStatistics()
        {
            m_TotalTransactions = 0;
            m_SuccessfulTransactions = 0;
            m_TotalRevenue = 0f;
            Log("Đã reset thống kê");
        }
        #endregion

        #region Private Methods
        private void KhoiTaoSystem()
        {
            Log("Khởi tạo NPC Shopping System...");
        }

        private void TimTatCaComponents()
        {
            // Tìm Camera Focus Controller
            if (m_CameraFocus == null)
            {
                m_CameraFocus = FindObjectOfType<CameraFocusController>();
            }

            // Tìm Dialogue System
            if (m_DialogueSystem == null)
            {
                m_DialogueSystem = FindObjectOfType<NPCDialogueSystem>();
            }

            // Tìm tất cả NPCs
            NPCShoppingAI[] npcs = FindObjectsOfType<NPCShoppingAI>();
            foreach (var npc in npcs)
            {
                RegisterNPC(npc);
            }

            // Tìm tất cả Shops
            ShopDisplayZone[] shops = FindObjectsOfType<ShopDisplayZone>();
            foreach (var shop in shops)
            {
                RegisterShop(shop);
            }

            Log($"Tìm thấy: {m_AllNPCs.Count} NPCs, {m_AllShops.Count} Shops");
        }

        private void DangKyEvents()
        {
            // Có thể đăng ký các events ở đây nếu cần
        }

        private void Log(string message)
        {
            if (m_HienThiLog)
            {
                Debug.Log($"[NPCShoppingManager] {message}");
            }
        }

        private void LogLoi(string message)
        {
            Debug.LogError($"[NPCShoppingManager] {message}");
        }
        #endregion

        #region Editor Methods
        private void OnValidate()
        {
            // Validation logic
        }

        private void OnDrawGizmos()
        {
            if (!m_ShowStatistics || !Application.isPlaying) return;

            // Có thể vẽ gizmos thống kê ở đây
        }
        #endregion

        #region Debug Methods
        [ContextMenu("Show System Info")]
        public void ShowSystemInfo()
        {
            Debug.Log("=== NPC SHOPPING SYSTEM INFO ===");
            Debug.Log($"System Initialized: {m_SystemInitialized}");
            Debug.Log($"Current Interacting NPC: {(m_CurrentInteractingNPC?.TenNPC ?? "None")}");
            Debug.Log($"Camera Focus: {(m_CameraFocus != null ? "✅" : "❌")}");
            Debug.Log($"Dialogue System: {(m_DialogueSystem != null ? "✅" : "❌")}");
            Debug.Log(GetSystemStatistics());
            Debug.Log("================================");
        }

        [ContextMenu("Force End All Interactions")]
        public void ForceEndAllInteractions()
        {
            EndInteraction();
            Debug.Log("Đã force end tất cả interactions");
        }
        #endregion
    }
}
