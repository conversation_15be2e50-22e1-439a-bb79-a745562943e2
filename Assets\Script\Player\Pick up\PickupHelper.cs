using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// Helper script để setup pickup system nhanh chóng
/// </summary>
public class PickupHelper : MonoBehaviour
{
    [Header("🚀 Quick Setup")]
    [SerializeField] private bool m_AutoSetupOnStart = true;

    [<PERSON><PERSON>("🎮 Pickup Settings")]
    [Serial<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Khoảng cách nhặt vật (m)")]
    private float m_PickupRange = 3f;

    [SerializeField, Tooltip("Phím nhặt vật")]
    private KeyCode m_PickupKey = KeyCode.E;

    [Header("📦 Carry Position")]
    [SerializeField, Tooltip("Khoảng cách phía trước camera")]
    private float m_CarryDistance = 1.5f;

    [Serial<PERSON><PERSON><PERSON>, Toolt<PERSON>("Độ cao so với camera (âm = thấp hơn)")]
    private float m_CarryHeight = -0.3f;

    [SerializeField, <PERSON><PERSON><PERSON>("<PERSON><PERSON> trí trái/phải (dương = phải)")]
    private float m_CarrySideOffset = 0.3f;

    [<PERSON><PERSON>("🎯 Drop Settings")]
    [SerializeField, Tooltip("Khoảng cách thả vật phía trước")]
    private float m_DropDistance = 1.5f;

    [SerializeField, Tooltip("Độ cao thả vật so với mặt đất")]
    private float m_DropHeight = 0.5f;

    [SerializeField, Tooltip("Kiểm tra va chạm khi thả")]
    private bool m_CheckDropCollision = true;
    
    void Start()
    {
        if (m_AutoSetupOnStart)
        {
            SetupPickupSystem();
        }
    }
    
    [ContextMenu("Setup Pickup System")]
    public void SetupPickupSystem()
    {
        // Thêm SimplePickup component nếu chưa có
        SimplePickup pickupComponent = GetComponent<SimplePickup>();
        if (pickupComponent == null)
        {
            pickupComponent = gameObject.AddComponent<SimplePickup>();
            Debug.Log("✅ Đã thêm SimplePickup component");
        }

        // Áp dụng settings từ PickupHelper
        ApplySettingsToPickupSystem(pickupComponent);

        Debug.Log("🎯 Pickup system đã sẵn sàng!");
        Debug.Log("📋 Hướng dẫn:");
        Debug.Log("1. Tạo GameObject bất kỳ");
        Debug.Log("2. Add component 'PickupableItem'");
        Debug.Log("3. Hoặc dùng 'Create Test Item' bên dưới");
        Debug.Log("4. Hover text sẽ tự động hiển thị khi nhìn vào vật");
        Debug.Log("5. Điều chỉnh settings trong PickupHelper Inspector");
    }

    [ContextMenu("Apply Settings to Pickup System")]
    public void ApplySettingsToPickupSystem()
    {
        SimplePickup pickupComponent = GetComponent<SimplePickup>();
        if (pickupComponent != null)
        {
            ApplySettingsToPickupSystem(pickupComponent);
            Debug.Log("✅ Đã áp dụng settings từ PickupHelper!");
        }
        else
        {
            Debug.LogWarning("⚠️ Không tìm thấy SimplePickup component!");
        }
    }

    private void ApplySettingsToPickupSystem(SimplePickup pickupSystem)
    {
        if (pickupSystem == null) return;

        // Sử dụng reflection để set private fields
        var type = typeof(SimplePickup);

        // Set pickup settings
        SetPrivateField(type, pickupSystem, "m_PickupRange", m_PickupRange);
        SetPrivateField(type, pickupSystem, "m_PickupKey", m_PickupKey);

        // Set carry position
        SetPrivateField(type, pickupSystem, "m_CarryDistance", m_CarryDistance);
        SetPrivateField(type, pickupSystem, "m_CarryHeight", m_CarryHeight);
        SetPrivateField(type, pickupSystem, "m_CarrySideOffset", m_CarrySideOffset);

        // Set drop settings
        SetPrivateField(type, pickupSystem, "m_DropDistance", m_DropDistance);
        SetPrivateField(type, pickupSystem, "m_DropHeight", m_DropHeight);
        SetPrivateField(type, pickupSystem, "m_CheckDropCollision", m_CheckDropCollision);

        // Update carry position
        pickupSystem.UpdateCarryPositionManual();

        #if UNITY_EDITOR
        UnityEditor.EditorUtility.SetDirty(pickupSystem);
        #endif
    }

    private void SetPrivateField(System.Type type, object instance, string fieldName, object value)
    {
        var field = type.GetField(fieldName, System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (field != null)
        {
            field.SetValue(instance, value);
        }
    }
    
    [ContextMenu("Create Test Item")]
    public void CreateTestItem()
    {
        // Tạo cube test
        GameObject testItem = GameObject.CreatePrimitive(PrimitiveType.Cube);
        testItem.name = "TestPickupItem";
        testItem.transform.position = transform.position + transform.forward * 3f + Vector3.up * 1f;
        
        // Thêm PickupableItem component
        PickupableItem pickupComponent = testItem.AddComponent<PickupableItem>();
        
        // Thiết lập màu sắc để dễ nhận biết
        Renderer renderer = testItem.GetComponent<Renderer>();
        if (renderer != null)
        {
            renderer.material.color = Color.yellow;
        }
        
        Debug.Log("✅ Đã tạo test pickup item!");
        Debug.Log("🎮 Nhìn vào vật và nhấn E để nhặt!");
    }
    
    [ContextMenu("Setup Selected Object as Pickupable")]
    public void SetupSelectedAsPickupable()
    {
        #if UNITY_EDITOR
        GameObject selected = Selection.activeGameObject;
        if (selected == null)
        {
            Debug.LogWarning("⚠️ Vui lòng chọn một GameObject trước!");
            return;
        }

        // Thêm PickupableItem nếu chưa có
        if (selected.GetComponent<PickupableItem>() == null)
        {
            selected.AddComponent<PickupableItem>();
        }

        Debug.Log($"✅ Đã setup {selected.name} thành pickupable item!");
        #else
        Debug.LogWarning("⚠️ Chức năng này chỉ hoạt động trong Unity Editor!");
        #endif
    }
    
    [ContextMenu("Create Multiple Test Items")]
    public void CreateMultipleTestItems()
    {
        for (int i = 0; i < 5; i++)
        {
            GameObject testItem = GameObject.CreatePrimitive(PrimitiveType.Cube);
            testItem.name = $"TestItem_{i + 1}";
            
            // Đặt ở vị trí khác nhau
            Vector3 position = transform.position + transform.forward * (3f + i * 1.5f) + Vector3.up * 1f;
            testItem.transform.position = position;
            
            // Thêm component
            testItem.AddComponent<PickupableItem>();
            
            // Màu sắc khác nhau
            Renderer renderer = testItem.GetComponent<Renderer>();
            if (renderer != null)
            {
                Color[] colors = { Color.red, Color.green, Color.blue, Color.yellow, Color.magenta };
                renderer.material.color = colors[i];
            }
        }
        
        Debug.Log("✅ Đã tạo 5 test items với màu sắc khác nhau!");
    }
}
