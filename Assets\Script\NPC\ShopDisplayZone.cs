using UnityEngine;
using System.Collections.Generic;

namespace NPCSystem
{
    /// <summary>
    /// Vùng trigger để trưng bày hàng hóa
    /// NPC sẽ đến đây để xem và mua hàng
    /// </summary>
    [RequireComponent(typeof(Collider))]
    public class ShopDisplayZone : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🏪 Shop Display Settings")]
        [SerializeField, Tooltip("ID của món hàng được trưng bày")]
        private string m_ItemID = "wood";
        
        [SerializeField, Tooltip("Tên hiển thị của món hàng")]
        private string m_TenHienThi = "Wood";
        
        [SerializeF<PERSON>, Toolt<PERSON>("Giá bán của món hàng")]
        private int m_GiaBan = 50;
        
        [SerializeField, Tooltip("GameObject hiển thị món hàng (3D model)")]
        private GameObject m_ItemDisplay;
        
        [Header("🎯 Zone Behavior")]
        [SerializeField, Tooltip("Kích thước vùng trigger")]
        private Vector3 m_KichThuocZone = new Vector3(2f, 2f, 2f);
        
        [SerializeField, Tooltip("Có hiển thị gizmo trong Scene view")]
        private bool m_HienThiGizmo = true;
        
        [SerializeField, Tooltip("Màu gizmo")]
        private Color m_MauGizmo = Color.green;
        
        [Header("🤖 NPC Interaction")]
        [SerializeField, Tooltip("Thời gian NPC đứng xem hàng (giây)")]
        private float m_ThoiGianXemHang = 3f;
        
        [SerializeField, Tooltip("Khoảng cách tối đa NPC có thể tương tác")]
        private float m_KhoangCachTuongTac = 1.5f;
        
        [SerializeField, Tooltip("Vị trí NPC nên đứng khi xem hàng")]
        private Transform m_ViTriNPCDung;
        
        [Header("🔧 Debug")]
        [SerializeField, Tooltip("Hiển thị log debug")]
        private bool m_HienThiLog = true;
        #endregion

        #region Private Fields
        private Collider m_Collider;
        private List<NPCShoppingAI> m_NPCsInZone = new List<NPCShoppingAI>();
        private bool m_DaKhoiTao = false;
        #endregion

        #region Properties
        public string ItemID => m_ItemID;
        public string TenHienThi => m_TenHienThi;
        public int GiaBan => m_GiaBan;
        public GameObject ItemDisplay => m_ItemDisplay;
        public Vector3 ViTriNPCDung => m_ViTriNPCDung != null ? m_ViTriNPCDung.position : transform.position;
        public float ThoiGianXemHang => m_ThoiGianXemHang;
        public bool CoNPCTrongZone => m_NPCsInZone.Count > 0;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            KhoiTaoComponent();
        }

        private void Start()
        {
            ThietLapZone();
            m_DaKhoiTao = true;
            Log($"Shop Display Zone khởi tạo: {m_TenHienThi} - {m_GiaBan} Lea");
        }

        private void OnTriggerEnter(Collider other)
        {
            if (!m_DaKhoiTao) return;

            NPCShoppingAI npc = other.GetComponent<NPCShoppingAI>();
            if (npc != null && !m_NPCsInZone.Contains(npc))
            {
                m_NPCsInZone.Add(npc);
                npc.OnEnterShopZone(this);
                Log($"NPC {npc.name} vào zone {m_TenHienThi}");
            }
        }

        private void OnTriggerExit(Collider other)
        {
            if (!m_DaKhoiTao) return;

            NPCShoppingAI npc = other.GetComponent<NPCShoppingAI>();
            if (npc != null && m_NPCsInZone.Contains(npc))
            {
                m_NPCsInZone.Remove(npc);
                npc.OnExitShopZone(this);
                Log($"NPC {npc.name} rời zone {m_TenHienThi}");
            }
        }

        private void OnDrawGizmos()
        {
            if (!m_HienThiGizmo) return;

            Gizmos.color = m_MauGizmo;
            Gizmos.DrawWireCube(transform.position, m_KichThuocZone);
            
            // Hiển thị vị trí NPC đứng
            if (m_ViTriNPCDung != null)
            {
                Gizmos.color = Color.blue;
                Gizmos.DrawWireSphere(m_ViTriNPCDung.position, 0.3f);
                Gizmos.DrawLine(transform.position, m_ViTriNPCDung.position);
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Kiểm tra NPC có thể mua hàng này không
        /// </summary>
        public bool CoTheBanChoNPC(NPCShoppingAI npc)
        {
            if (npc == null) return false;

            NPCCurrencyManager currency = npc.GetComponent<NPCCurrencyManager>();
            if (currency == null) return false;

            return currency.CoTheMua(m_GiaBan);
        }

        /// <summary>
        /// Thực hiện bán hàng cho NPC
        /// </summary>
        public bool BanHangChoNPC(NPCShoppingAI npc)
        {
            if (!CoTheBanChoNPC(npc))
            {
                Log($"Không thể bán {m_TenHienThi} cho NPC {npc.name}");
                return false;
            }

            NPCCurrencyManager currency = npc.GetComponent<NPCCurrencyManager>();
            if (currency.ThucHienMua(m_GiaBan))
            {
                Log($"Đã bán {m_TenHienThi} cho NPC {npc.name} với giá {m_GiaBan} Lea");
                
                // Có thể thêm logic tích hợp với Economy System ở đây
                // Ví dụ: thêm tiền vào player inventory
                
                return true;
            }

            return false;
        }

        /// <summary>
        /// Lấy khoảng cách từ NPC đến vị trí đứng
        /// </summary>
        public float GetKhoangCachDenViTriDung(Vector3 viTriNPC)
        {
            return Vector3.Distance(viTriNPC, ViTriNPCDung);
        }

        /// <summary>
        /// Kiểm tra NPC có đang ở vị trí phù hợp để mua hàng không
        /// </summary>
        public bool NPCOViTriMuaHang(NPCShoppingAI npc)
        {
            if (npc == null) return false;
            
            float khoangCach = GetKhoangCachDenViTriDung(npc.transform.position);
            return khoangCach <= m_KhoangCachTuongTac;
        }

        /// <summary>
        /// Thiết lập thông tin món hàng
        /// </summary>
        public void ThietLapMonHang(string itemID, string tenHienThi, int giaBan, GameObject itemDisplay = null)
        {
            m_ItemID = itemID;
            m_TenHienThi = tenHienThi;
            m_GiaBan = giaBan;
            
            if (itemDisplay != null)
            {
                m_ItemDisplay = itemDisplay;
            }

            Log($"Đã thiết lập món hàng: {m_TenHienThi} - {m_GiaBan} Lea");
        }

        /// <summary>
        /// Lấy danh sách NPC trong zone
        /// </summary>
        public List<NPCShoppingAI> GetNPCsInZone()
        {
            // Loại bỏ các NPC null
            m_NPCsInZone.RemoveAll(npc => npc == null);
            return new List<NPCShoppingAI>(m_NPCsInZone);
        }
        #endregion

        #region Private Methods
        private void KhoiTaoComponent()
        {
            m_Collider = GetComponent<Collider>();
            if (m_Collider == null)
            {
                m_Collider = gameObject.AddComponent<BoxCollider>();
            }
            
            m_Collider.isTrigger = true;
        }

        private void ThietLapZone()
        {
            // Thiết lập kích thước collider
            if (m_Collider is BoxCollider boxCollider)
            {
                boxCollider.size = m_KichThuocZone;
            }

            // Tạo vị trí NPC đứng nếu chưa có
            if (m_ViTriNPCDung == null)
            {
                GameObject viTriDung = new GameObject("NPC_Standing_Position");
                viTriDung.transform.SetParent(transform);
                viTriDung.transform.localPosition = Vector3.forward * (m_KichThuocZone.z * 0.4f);
                m_ViTriNPCDung = viTriDung.transform;
            }

            // Hiển thị item display nếu có
            if (m_ItemDisplay != null && !m_ItemDisplay.activeInHierarchy)
            {
                m_ItemDisplay.SetActive(true);
            }
        }

        private void Log(string message)
        {
            if (m_HienThiLog)
            {
                Debug.Log($"[ShopDisplayZone-{gameObject.name}] {message}");
            }
        }
        #endregion

        #region Editor Methods
        private void OnValidate()
        {
            if (m_GiaBan < 0) m_GiaBan = 0;
            if (m_ThoiGianXemHang < 0) m_ThoiGianXemHang = 0;
            if (m_KhoangCachTuongTac < 0) m_KhoangCachTuongTac = 0;
            
            // Cập nhật kích thước collider trong editor
            if (m_Collider != null && m_Collider is BoxCollider boxCollider)
            {
                boxCollider.size = m_KichThuocZone;
            }
        }
        #endregion
    }
}
