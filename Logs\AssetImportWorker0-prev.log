Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-17T06:42:52Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
E:/2025/Boder/My boder 3
-logFile
Logs/AssetImportWorker0.log
-srvPort
62203
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/My boder 3
E:/2025/Boder/My boder 3
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [11056]  Target information:

Player connection [11056]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2030208158 [EditorId] 2030208158 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [11056] Host joined multi-casting on [***********:54997]...
Player connection [11056] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 0.99 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/My boder 3/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56288
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002447 seconds.
- Loaded All Assemblies, in  0.602 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.375 seconds
Domain Reload Profiling: 978ms
	BeginReloadAssembly (257ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (222ms)
		LoadAssemblies (257ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (217ms)
			TypeCache.Refresh (215ms)
				TypeCache.ScanAssembly (193ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (376ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (345ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (186ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.971 seconds
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.788 seconds
Domain Reload Profiling: 1759ms
	BeginReloadAssembly (162ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (733ms)
		LoadAssemblies (399ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (418ms)
			TypeCache.Refresh (328ms)
				TypeCache.ScanAssembly (305ms)
			BuildScriptInfoCaches (73ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (788ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (554ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (121ms)
			ProcessInitializeOnLoadAttributes (367ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 225 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6646 unused Assets / (8.0 MB). Loaded Objects now: 7320.
Memory consumption went from 171.3 MB to 163.3 MB.
Total: 14.133500 ms (FindLiveObjects: 0.869400 ms CreateObjectMapping: 1.093300 ms MarkObjects: 6.456300 ms  DeleteObjects: 5.713500 ms)

========================================================================
Received Import Request.
  Time since last request: 521700.926917 seconds.
  path: Assets/Script/Player/PickupInventoryAdapter.cs
  artifactKey: Guid(bc92c889b07cffb4c8f8b470bb371370) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/Player/PickupInventoryAdapter.cs using Guid(bc92c889b07cffb4c8f8b470bb371370) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f8beab29ed85e0f9777d0c3a2ecc0501') in 0.0051656 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Script/Player/PickupUI.cs
  artifactKey: Guid(304ac7b99b7c72a4583297bbaf0dec76) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/Player/PickupUI.cs using Guid(304ac7b99b7c72a4583297bbaf0dec76) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e509628ed2ac47dca3fd248088044176') in 0.0005134 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6627 unused Assets / (7.7 MB). Loaded Objects now: 7314.
Memory consumption went from 140.7 MB to 133.0 MB.
Total: 12.132500 ms (FindLiveObjects: 0.942100 ms CreateObjectMapping: 1.173000 ms MarkObjects: 5.668500 ms  DeleteObjects: 4.347000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 17.025882 seconds.
  path: Assets/Script/Player/README_PICKUP_SYSTEM.md
  artifactKey: Guid(72d4d3b22d729b64f963e01825b2102a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/Player/README_PICKUP_SYSTEM.md using Guid(72d4d3b22d729b64f963e01825b2102a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cc2635730d6b4e3567b20f53b1469ebd') in 0.0394406 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6624 unused Assets / (7.3 MB). Loaded Objects now: 7312.
Memory consumption went from 140.7 MB to 133.4 MB.
Total: 11.597200 ms (FindLiveObjects: 0.763500 ms CreateObjectMapping: 0.906400 ms MarkObjects: 5.813200 ms  DeleteObjects: 4.112900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 28.598336 seconds.
  path: Assets/Script/Player/Pick up
  artifactKey: Guid(b4ebc7672febe3b4bb50d0c4d46ca187) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/Player/Pick up using Guid(b4ebc7672febe3b4bb50d0c4d46ca187) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '28c5c327fbe04a1eecf641cdfd6ac5c3') in 0.0051369 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.830202 seconds.
  path: Assets/Script/Player/PlayerSystemTester.cs
  artifactKey: Guid(f9ddb8d4eb1fe3e4b8c4da4e7a14e190) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/Player/PlayerSystemTester.cs using Guid(f9ddb8d4eb1fe3e4b8c4da4e7a14e190) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '82ac01b85d772f0c5c3ae4ad9589366c') in 0.0007036 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.348093 seconds.
  path: Assets/Script/Player/QUICK_SETUP_PICKUP.md
  artifactKey: Guid(312f814524f9a834b8df0facbb87b229) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/Player/QUICK_SETUP_PICKUP.md using Guid(312f814524f9a834b8df0facbb87b229) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0cf5d612e2cca874be48c2b2940f79f7') in 0.0009954 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6629 unused Assets / (7.6 MB). Loaded Objects now: 7317.
Memory consumption went from 140.7 MB to 133.0 MB.
Total: 13.596000 ms (FindLiveObjects: 1.019300 ms CreateObjectMapping: 1.298200 ms MarkObjects: 6.676200 ms  DeleteObjects: 4.600500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 482.439718 seconds.
  path: Assets/Script/Player/PlayerSetupUtility.cs
  artifactKey: Guid(e825650302063a342b0856b3ea94109b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/Player/PlayerSetupUtility.cs using Guid(e825650302063a342b0856b3ea94109b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3a2e12380a5534cec8967b4f4ac77b00') in 0.0059531 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.03 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6631 unused Assets / (7.4 MB). Loaded Objects now: 7319.
Memory consumption went from 140.7 MB to 133.3 MB.
Total: 10.786900 ms (FindLiveObjects: 0.805700 ms CreateObjectMapping: 0.749500 ms MarkObjects: 5.347400 ms  DeleteObjects: 3.883000 ms)

Prepare: number of updated asset objects reloaded= 0
