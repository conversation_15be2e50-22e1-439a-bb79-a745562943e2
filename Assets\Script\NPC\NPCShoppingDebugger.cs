using UnityEngine;
using System.Collections.Generic;

namespace NPCSystem
{
    /// <summary>
    /// Debug helper cho NPC Shopping System
    /// Hi<PERSON>n thị thông tin debug và kiểm tra hệ thống
    /// </summary>
    public class NPCShoppingDebugger : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🔍 Debug Settings")]
        [SerializeField, <PERSON>lt<PERSON>("Hiển thị debug UI")]
        private bool m_HienThiDebugUI = true;
        
        [SerializeField, Tooltip("Tự động kiểm tra hệ thống")]
        private bool m_TuDongKiemTra = true;
        
        [SerializeField, Tooltip("Thời gian giữa các lần kiểm tra")]
        private float m_ThoiGianKiemTra = 5f;
        
        [Header("🎯 References")]
        [SerializeField, Tooltip("NPC cần debug")]
        private NPCShoppingAI m_TargetNPC;
        
        [SerializeField, Tooltip("Shop zone cần debug")]
        private ShopDisplayZone m_TargetShop;
        #endregion

        #region Private Fields
        private float m_LanKiemTraCuoi = 0f;
        private List<string> m_DebugMessages = new List<string>();
        private Vector2 m_ScrollPosition = Vector2.zero;
        #endregion

        #region Unity Lifecycle
        private void Start()
        {
            if (m_TargetNPC == null)
            {
                m_TargetNPC = FindObjectOfType<NPCShoppingAI>();
            }
            
            if (m_TargetShop == null)
            {
                m_TargetShop = FindObjectOfType<ShopDisplayZone>();
            }
            
            ThemDebugMessage("NPCShoppingDebugger khởi tạo");
        }

        private void Update()
        {
            if (m_TuDongKiemTra && Time.time - m_LanKiemTraCuoi >= m_ThoiGianKiemTra)
            {
                KiemTraHeThong();
                m_LanKiemTraCuoi = Time.time;
            }
        }

        private void OnGUI()
        {
            if (!m_HienThiDebugUI) return;

            GUILayout.BeginArea(new Rect(10, 10, 400, Screen.height - 20));
            GUILayout.BeginVertical("box");

            GUILayout.Label("🛍️ NPC Shopping System Debug", GUI.skin.label);

            // Thông tin NPC
            HienThiThongTinNPC();

            GUILayout.Space(10);

            // Thông tin Shop
            HienThiThongTinShop();

            GUILayout.Space(10);

            // Buttons
            HienThiButtons();

            GUILayout.Space(10);

            // Debug messages
            HienThiDebugMessages();

            GUILayout.EndVertical();
            GUILayout.EndArea();
        }
        #endregion

        #region Public Methods
        [ContextMenu("Kiểm Tra Hệ Thống")]
        public void KiemTraHeThong()
        {
            ThemDebugMessage("=== KIỂM TRA HỆ THỐNG ===");
            
            // Kiểm tra NPC
            KiemTraNPC();
            
            // Kiểm tra Shop
            KiemTraShop();
            
            // Kiểm tra khoảng cách
            KiemTraKhoangCach();
            
            // Kiểm tra NavMesh
            KiemTraNavMesh();
            
            ThemDebugMessage("=== KẾT THÚC KIỂM TRA ===");
        }

        [ContextMenu("Force NPC Tìm Shop")]
        public void ForceNPCTimShop()
        {
            if (m_TargetNPC != null)
            {
                // Gọi method private thông qua reflection
                var method = typeof(NPCShoppingAI).GetMethod("TimShopZoneGanNhat", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                method?.Invoke(m_TargetNPC, null);
                
                ThemDebugMessage("Đã force NPC tìm shop");
            }
        }

        [ContextMenu("Reset NPC Money")]
        public void ResetNPCMoney()
        {
            if (m_TargetNPC != null)
            {
                NPCCurrencyManager currency = m_TargetNPC.GetComponent<NPCCurrencyManager>();
                if (currency != null)
                {
                    currency.ResetTien();
                    ThemDebugMessage("Đã reset tiền NPC");
                }
            }
        }
        #endregion

        #region Private Methods
        private void HienThiThongTinNPC()
        {
            GUILayout.Label("🤖 NPC Information:", GUI.skin.label);
            
            if (m_TargetNPC != null)
            {
                GUILayout.Label($"Tên: {m_TargetNPC.TenNPC}");
                GUILayout.Label($"Trạng thái: {m_TargetNPC.TrangThaiHienTai}");
                GUILayout.Label($"Có thể click: {m_TargetNPC.PlayerCoTheClick}");
                
                NPCCurrencyManager currency = m_TargetNPC.GetComponent<NPCCurrencyManager>();
                if (currency != null)
                {
                    GUILayout.Label($"Tiền: {currency.SoTienHienTai} Lea");
                    GUILayout.Label($"Có thể mua: {currency.CoTheMuaHang}");
                    GUILayout.Label($"Có thể chi: {currency.GetSoTienCoTheChi()} Lea");
                }
                
                if (m_TargetNPC.CurrentShopZone != null)
                {
                    GUILayout.Label($"Shop hiện tại: {m_TargetNPC.CurrentShopZone.TenHienThi}");
                }
                else
                {
                    GUILayout.Label("Shop hiện tại: Không có");
                }
            }
            else
            {
                GUILayout.Label("❌ Không tìm thấy NPC");
            }
        }

        private void HienThiThongTinShop()
        {
            GUILayout.Label("🏪 Shop Information:", GUI.skin.label);
            
            if (m_TargetShop != null)
            {
                GUILayout.Label($"Tên: {m_TargetShop.TenHienThi}");
                GUILayout.Label($"Giá: {m_TargetShop.GiaBan} Lea");
                GUILayout.Label($"NPCs trong zone: {m_TargetShop.GetNPCsInZone().Count}");
                
                if (m_TargetNPC != null)
                {
                    bool coTheBan = m_TargetShop.CoTheBanChoNPC(m_TargetNPC);
                    GUILayout.Label($"Có thể bán cho NPC: {(coTheBan ? "✅" : "❌")}");
                }
            }
            else
            {
                GUILayout.Label("❌ Không tìm thấy Shop");
            }
        }

        private void HienThiButtons()
        {
            if (GUILayout.Button("Kiểm Tra Hệ Thống"))
            {
                KiemTraHeThong();
            }
            
            if (GUILayout.Button("Force NPC Tìm Shop"))
            {
                ForceNPCTimShop();
            }
            
            if (GUILayout.Button("Reset NPC Money"))
            {
                ResetNPCMoney();
            }
            
            if (GUILayout.Button("Clear Debug Log"))
            {
                m_DebugMessages.Clear();
            }
        }

        private void HienThiDebugMessages()
        {
            GUILayout.Label("📝 Debug Messages:", GUI.skin.label);

            m_ScrollPosition = GUILayout.BeginScrollView(m_ScrollPosition, GUILayout.Height(200));

            for (int i = m_DebugMessages.Count - 1; i >= 0; i--)
            {
                GUILayout.Label(m_DebugMessages[i], GUI.skin.label);
            }

            GUILayout.EndScrollView();
        }

        private void KiemTraNPC()
        {
            if (m_TargetNPC == null)
            {
                ThemDebugMessage("❌ Không có NPC target");
                return;
            }
            
            // Kiểm tra components
            NPCCurrencyManager currency = m_TargetNPC.GetComponent<NPCCurrencyManager>();
            UnityEngine.AI.NavMeshAgent agent = m_TargetNPC.GetComponent<UnityEngine.AI.NavMeshAgent>();
            
            ThemDebugMessage($"NPC: {m_TargetNPC.name}");
            ThemDebugMessage($"- Currency Manager: {(currency != null ? "✅" : "❌")}");
            ThemDebugMessage($"- NavMesh Agent: {(agent != null ? "✅" : "❌")}");
            
            if (currency != null)
            {
                ThemDebugMessage($"- Tiền: {currency.SoTienHienTai}/{currency.GetSoTienCoTheChi()} Lea");
            }
        }

        private void KiemTraShop()
        {
            ShopDisplayZone[] shops = FindObjectsOfType<ShopDisplayZone>();
            ThemDebugMessage($"Tìm thấy {shops.Length} shop zones");
            
            foreach (var shop in shops)
            {
                ThemDebugMessage($"- {shop.name}: {shop.TenHienThi} ({shop.GiaBan} Lea)");
                
                // Kiểm tra collider
                Collider col = shop.GetComponent<Collider>();
                if (col != null)
                {
                    ThemDebugMessage($"  Collider: {(col.isTrigger ? "✅ Trigger" : "❌ Không phải trigger")}");
                }
                else
                {
                    ThemDebugMessage($"  ❌ Không có Collider");
                }
            }
        }

        private void KiemTraKhoangCach()
        {
            if (m_TargetNPC != null && m_TargetShop != null)
            {
                float khoangCach = Vector3.Distance(m_TargetNPC.transform.position, m_TargetShop.transform.position);
                ThemDebugMessage($"Khoảng cách NPC-Shop: {khoangCach:F1}m");
            }
        }

        private void KiemTraNavMesh()
        {
            if (m_TargetNPC != null)
            {
                UnityEngine.AI.NavMeshAgent agent = m_TargetNPC.GetComponent<UnityEngine.AI.NavMeshAgent>();
                if (agent != null)
                {
                    ThemDebugMessage($"NavMesh Agent:");
                    ThemDebugMessage($"- Enabled: {agent.enabled}");
                    ThemDebugMessage($"- On NavMesh: {agent.isOnNavMesh}");
                    ThemDebugMessage($"- Has Path: {agent.hasPath}");
                    ThemDebugMessage($"- Path Status: {agent.pathStatus}");
                }
            }
        }

        private void ThemDebugMessage(string message)
        {
            string timeStamp = System.DateTime.Now.ToString("HH:mm:ss");
            m_DebugMessages.Add($"[{timeStamp}] {message}");
            
            // Giới hạn số lượng messages
            if (m_DebugMessages.Count > 50)
            {
                m_DebugMessages.RemoveAt(0);
            }
            
            Debug.Log($"[NPCShoppingDebugger] {message}");
        }
        #endregion
    }
}
