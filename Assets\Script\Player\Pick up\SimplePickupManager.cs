using UnityEngine;

/// <summary>
/// Simple Pickup Manager - Version đơn giản để test
/// </summary>
public class SimplePickupManager : MonoBehaviour
{
    [Header("🎮 Basic Setup")]
    [SerializeField] private bool m_AutoSetup = true;
    [SerializeField] private bool m_ShowDebugLogs = true;
    
    private void Start()
    {
        if (m_AutoSetup)
        {
            SetupPickupSystem();
        }
    }
    
    [ContextMenu("Setup Pickup System")]
    public void SetupPickupSystem()
    {
        // Thêm PickupSystem nếu chưa có
        if (GetComponent<PlayerSystem.PickupSystem>() == null)
        {
            gameObject.AddComponent<PlayerSystem.PickupSystem>();
            if (m_ShowDebugLogs)
                Debug.Log("✅ Đã thêm PickupSystem");
        }
        
        // Thêm PickupEffects nếu chưa có
        if (GetComponent<PlayerSystem.PickupEffects>() == null)
        {
            gameObject.AddComponent<PlayerSystem.PickupEffects>();
            if (m_ShowDebugLogs)
                Debug.Log("✅ Đã thêm PickupEffects");
        }
        
        if (m_ShowDebugLogs)
            Debug.Log("🎯 Pickup System setup hoàn tất!");
    }
    
    [ContextMenu("Create Test Item")]
    public void CreateTestItem()
    {
        GameObject cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
        cube.transform.position = transform.position + transform.forward * 2f;
        cube.name = "TestPickupItem";
        cube.tag = "Pickupable";
        cube.AddComponent<PlayerSystem.PickupItem>();
        
        if (m_ShowDebugLogs)
            Debug.Log("✅ Đã tạo test pickup item");
    }
}
