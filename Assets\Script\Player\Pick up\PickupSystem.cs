using UnityEngine;
using System.Collections.Generic;

namespace PlayerSystem
{
    /// <summary>
    /// <PERSON>ệ thống pickup chính sử dụng raycast để phát hiện và tương tác với vật phẩm
    /// Hỗ trợ phím E/F để nhặt và thả vật phẩm
    /// </summary>
    public class PickupSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎯 Pickup Detection")]
        [SerializeField, Toolt<PERSON>("Khoảng cách tối đa để pickup")]
        private float m_PickupRange = 3f;
        
        [SerializeField, Tooltip("Layer mask cho vật phẩm có thể pickup")]
        private LayerMask m_PickupLayerMask = -1;
        
        [SerializeField, Tooltip("Tag cho vật phẩm có thể pickup")]
        private string m_PickupTag = "Pickupable";

        [Header("🎮 Input Settings")]
        [SerializeField, <PERSON>lt<PERSON>("Phím để pickup/drop vật phẩm")]
        private KeyCode m_PickupKey = KeyCode.E;
        
        [SerializeField, Tooltip("Phím thay thế để pickup/drop")]
        private KeyCode m_AlternatePickupKey = KeyCode.F;

        [Header("🎒 Carrying Settings")]
        [SerializeField, Tooltip("Số lượng vật phẩm tối đa có thể mang")]
        private int m_MaxCarryItems = 1;
        
        [SerializeField, Tooltip("Vị trí để giữ vật phẩm khi pickup")]
        private Transform m_CarryPosition;
        
        [SerializeField, Tooltip("Khoảng cách thả vật phẩm")]
        private float m_DropDistance = 2f;

        [Header("🔧 References")]
        [SerializeField, Tooltip("Camera để raycast (tự động tìm nếu null)")]
        private Camera m_PlayerCamera;
        
        [SerializeField, Tooltip("UI hiển thị pickup prompt")]
        private PickupUI m_PickupUI;
        
        [SerializeField, Tooltip("Effects cho pickup")]
        private PickupEffects m_PickupEffects;

        [Header("⚙️ Settings")]
        [SerializeField, Tooltip("Có hiển thị debug info")]
        private bool m_ShowDebugInfo = false;
        
        [SerializeField, Tooltip("Tần suất kiểm tra pickup (Hz)")]
        private float m_DetectionFrequency = 10f;
        #endregion

        #region Private Fields
        private PickupItem m_CurrentDetectedItem;
        private List<PickupItem> m_CarriedItems = new List<PickupItem>();
        private float m_LastDetectionTime;
        private bool m_IsInitialized = false;
        #endregion

        #region Properties
        /// <summary>Có đang mang vật phẩm không</summary>
        public bool IsCarryingItems => m_CarriedItems.Count > 0;
        
        /// <summary>Số lượng vật phẩm đang mang</summary>
        public int CarriedItemsCount => m_CarriedItems.Count;
        
        /// <summary>Vật phẩm hiện tại đang được phát hiện</summary>
        public PickupItem CurrentDetectedItem => m_CurrentDetectedItem;
        
        /// <summary>Có thể pickup thêm vật phẩm không</summary>
        public bool CanPickupMore => m_CarriedItems.Count < m_MaxCarryItems;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            TimCacComponent();
        }

        private void Start()
        {
            KhoiTaoHeTong();
        }

        private void Update()
        {
            if (!m_IsInitialized) return;

            XuLyInput();
            CapNhatDetection();
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Thử pickup một vật phẩm cụ thể
        /// </summary>
        public bool TryPickupItem(PickupItem item)
        {
            if (item == null || !CanPickupMore) return false;
            
            if (item.TryPickup(null)) // Sẽ tích hợp với inventory sau
            {
                ThemVatPhamVaoCarry(item);
                return true;
            }
            
            return false;
        }

        /// <summary>
        /// Thả tất cả vật phẩm đang mang
        /// </summary>
        public void DropAllItems()
        {
            for (int i = m_CarriedItems.Count - 1; i >= 0; i--)
            {
                DropItem(m_CarriedItems[i]);
            }
        }

        /// <summary>
        /// Thả một vật phẩm cụ thể
        /// </summary>
        public bool DropItem(PickupItem item)
        {
            if (item == null || !m_CarriedItems.Contains(item)) return false;

            Vector3 dropPosition = TinhViTriTha();
            item.Drop(dropPosition);
            m_CarriedItems.Remove(item);
            
            // Hiệu ứng thả
            if (m_PickupEffects != null)
            {
                m_PickupEffects.PlayDropEffect(dropPosition);
            }

            if (m_ShowDebugInfo)
            {
                Debug.Log($"[PickupSystem] Đã thả: {item.TenHienThi}");
            }

            return true;
        }
        #endregion

        #region Private Methods
        private void TimCacComponent()
        {
            // Tìm camera nếu chưa có
            if (m_PlayerCamera == null)
            {
                m_PlayerCamera = Camera.main;
                if (m_PlayerCamera == null)
                {
                    m_PlayerCamera = GetComponentInChildren<Camera>();
                }
            }

            // Tìm carry position nếu chưa có
            if (m_CarryPosition == null)
            {
                Transform carryTransform = transform.Find("CarryPosition");
                if (carryTransform == null)
                {
                    GameObject carryObj = new GameObject("CarryPosition");
                    carryObj.transform.SetParent(transform);
                    carryObj.transform.localPosition = Vector3.forward * 1.5f + Vector3.up * 0.5f;
                    m_CarryPosition = carryObj.transform;
                }
                else
                {
                    m_CarryPosition = carryTransform;
                }
            }

            // Tìm UI và Effects
            if (m_PickupUI == null)
            {
                m_PickupUI = FindObjectOfType<PickupUI>();
            }
            
            if (m_PickupEffects == null)
            {
                m_PickupEffects = GetComponent<PickupEffects>();
            }
        }

        private void KhoiTaoHeTong()
        {
            if (m_PlayerCamera == null)
            {
                Debug.LogWarning("[PickupSystem] Không tìm thấy Player Camera!");
                return;
            }

            m_IsInitialized = true;
            
            if (m_ShowDebugInfo)
            {
                Debug.Log("[PickupSystem] Đã khởi tạo thành công");
            }
        }

        private void XuLyInput()
        {
            if (Input.GetKeyDown(m_PickupKey) || Input.GetKeyDown(m_AlternatePickupKey))
            {
                if (IsCarryingItems)
                {
                    // Thả vật phẩm
                    DropAllItems();
                }
                else if (m_CurrentDetectedItem != null)
                {
                    // Nhặt vật phẩm
                    TryPickupItem(m_CurrentDetectedItem);
                }
            }
        }

        private void CapNhatDetection()
        {
            // Throttle detection để tối ưu performance
            if (Time.time - m_LastDetectionTime < 1f / m_DetectionFrequency)
                return;

            m_LastDetectionTime = Time.time;

            PickupItem previousItem = m_CurrentDetectedItem;
            m_CurrentDetectedItem = PhacHienVatPham();

            // Cập nhật UI và effects
            if (m_CurrentDetectedItem != previousItem)
            {
                CapNhatHighlight(previousItem, m_CurrentDetectedItem);
                CapNhatUI();
            }
        }

        private PickupItem PhacHienVatPham()
        {
            if (m_PlayerCamera == null) return null;

            RaycastHit hit;
            Vector3 rayOrigin = m_PlayerCamera.transform.position;
            Vector3 rayDirection = m_PlayerCamera.transform.forward;

            if (Physics.Raycast(rayOrigin, rayDirection, out hit, m_PickupRange, m_PickupLayerMask))
            {
                if (hit.collider.CompareTag(m_PickupTag))
                {
                    PickupItem item = hit.collider.GetComponent<PickupItem>();
                    if (item != null && item.CoThePickup)
                    {
                        return item;
                    }
                }
            }

            return null;
        }

        private void CapNhatHighlight(PickupItem previousItem, PickupItem currentItem)
        {
            // Tắt highlight cho item trước
            if (previousItem != null)
            {
                previousItem.Highlight(false);
            }

            // Bật highlight cho item hiện tại
            if (currentItem != null)
            {
                currentItem.Highlight(true);
            }
        }

        private void CapNhatUI()
        {
            if (m_PickupUI == null) return;

            if (m_CurrentDetectedItem != null)
            {
                m_PickupUI.HienThiPrompt(m_CurrentDetectedItem);
            }
            else
            {
                m_PickupUI.AnPrompt();
            }
        }

        private void ThemVatPhamVaoCarry(PickupItem item)
        {
            m_CarriedItems.Add(item);
            
            // Di chuyển vật phẩm đến vị trí carry
            if (m_CarryPosition != null)
            {
                item.transform.SetParent(m_CarryPosition);
                item.transform.localPosition = Vector3.zero;
                item.transform.localRotation = Quaternion.identity;
            }

            // Hiệu ứng pickup
            if (m_PickupEffects != null)
            {
                m_PickupEffects.PlayPickupEffect(item.transform.position);
            }

            if (m_ShowDebugInfo)
            {
                Debug.Log($"[PickupSystem] Đã nhặt: {item.TenHienThi}");
            }
        }

        private Vector3 TinhViTriTha()
        {
            Vector3 dropPosition = transform.position + transform.forward * m_DropDistance;
            
            // Kiểm tra va chạm để tránh thả vào tường
            RaycastHit hit;
            if (Physics.Raycast(transform.position, transform.forward, out hit, m_DropDistance))
            {
                dropPosition = hit.point - transform.forward * 0.5f;
            }

            return dropPosition;
        }
        #endregion

        #region Debug
        private void OnDrawGizmosSelected()
        {
            if (m_PlayerCamera != null)
            {
                // Vẽ pickup range
                Gizmos.color = Color.yellow;
                Gizmos.DrawRay(m_PlayerCamera.transform.position, 
                              m_PlayerCamera.transform.forward * m_PickupRange);
                
                // Vẽ carry position
                if (m_CarryPosition != null)
                {
                    Gizmos.color = Color.green;
                    Gizmos.DrawWireSphere(m_CarryPosition.position, 0.1f);
                }
            }
        }
        #endregion
    }
}
